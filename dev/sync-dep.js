/**
 * 更新子仓库和tee-h5依赖不一致的情况
 */

const fs = require('fs-extra');
const path = require('path');
const { spawn } = require('child_process');

const NEED_SYNC_SUB_MODULES = ['ext-tee-wsc-goods', 'ext-tee-wsc-trade'];
const SUB_MODULE_PATHS = NEED_SYNC_SUB_MODULES.map((moduleName) => {
  return path.join(__dirname, '../src', moduleName);
});
const MAIN_CONFIG = fs.readJsonSync('./package.json');
const MAIN_DEP = {
  ...MAIN_CONFIG.dependencies,
  // 优先以resolution为准
  ...MAIN_CONFIG.resolutions,
};

SUB_MODULE_PATHS.forEach((modulePath) => {
  const config = fs.readJsonSync(modulePath + '/package.json');
  const moduleDep = config.devDependencies;

  let isChange = false;
  Object.keys(moduleDep).forEach((key) => {
    if (!MAIN_DEP[key]) return;
    if (MAIN_DEP[key] !== moduleDep[key]) {
      isChange = true;
      moduleDep[key] = MAIN_DEP[key];
    }
  });
  if (isChange) {
    fs.writeJSONSync(modulePath + '/package.json', config, { spaces: 2 });
    spawn('yarn', {
      cwd: modulePath,
    });
  }
});
