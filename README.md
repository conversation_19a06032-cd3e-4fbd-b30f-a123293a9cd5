# 多端 H5 调试仓库

### 快速上手

```bash
# 安装本地依赖
yarn

# 启动服务
yarn dev:web
```

## 本地开发

> 本地开发需参考[中台化开发文档](https://fedoc.qima-inc.com/biz-engine/guides)

### 纯 client 代理配置

> 在对应 Node 不需要修改的情况下，可直接通过 ranta 开发者工具将前端代理到本地，进行前端开发

安装 [ranta 开发者工具](https://fedoc.qima-inc.com/biz-engine/guides/tools/ranta-devtools)， 通过操作面板切换。

![choice-mode](https://img01.yzcdn.cn/upload_files/2021/10/15/FnWJFUgbFOr9SersqshWIWw9BIWi.png)

### 依赖 Node 开发

若需要依赖 Node 进行调试开发，需拉取对应的 Node 仓库，按照 Node 开发文档操作即可 (本地启动项目、修改代码、配置 Node 转发规则等)。

#### 仓库与服务

- 交易 Node 仓库：[wsc-h5-trade](https://gitlab.qima-inc.com/wsc-node/wsc-h5-trade)
- 商品 Node 仓库：[wsc-h5-goods](https://gitlab.qima-inc.com/wsc-node/wsc-h5-goods)
- ......

### 基础依赖升级

中台化核心能力由基础库提供，为了便于管理，避免开发误操作。我们将部分依赖收敛到基础库中。

以下依赖升级需要联系 @木樨 ，在项目内升级无效：

- @youzan/ranta-tee-vue
- @youzan/tee
- @youzan/ranta-tee-plugin-logger
- @youzan/ranta-plugin-dmc

详细说明可参考: [中台化基础库](https://fedoc.qima-inc.com/biz-engine/guides/advanced/base-library)

## 参考链接

- [中台化仓库结构](https://fedoc.qima-inc.com/biz-engine/guides/basic/repo-struct)
- [中台化开发文档](https://fedoc.qima-inc.com/ranta)
- [多端开发文档](http://fedoc.qima-inc.com/tee)
