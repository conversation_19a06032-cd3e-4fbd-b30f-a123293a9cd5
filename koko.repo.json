{"repos": [{"url": "***********************:weapp/ext-tee-wsc-decorate.git", "name": "ext-tee-wsc-decorate", "path": "src", "branch": "release/2025-08-28-11-49-19"}, {"url": "***********************:weapp/ext-tee-wsc-decorate-h5.git", "name": "ext-tee-wsc-decorate-h5", "path": "src", "branch": "release/2025-03-19-14-10-00"}, {"url": "***********************:weapp/wsc-tee-statcenter.git", "name": "ext-tee-wsc-statcenter", "path": "src", "branch": "release/2025-08-12-10-00-56"}, {"url": "***********************:weapp/ext-tee-assets.git", "name": "ext-tee-assets", "path": "src", "branch": "release/2025-07-08-11-35-29"}, {"url": "***********************:weapp/ext-tee-passport.git", "name": "ext-tee-passport", "path": "src", "branch": "release/2025-07-21-16-48-00"}, {"url": "***********************:weapp/ext-tee-shop.git", "name": "ext-tee-shop", "path": "src", "branch": "release/2025-06-24-17-04-11"}, {"url": "***********************:weapp/ext-tee-wsc-goods.git", "name": "ext-tee-wsc-goods", "path": "src", "branch": "release/2025-08-28-11-49-19"}, {"url": "***********************:weapp/ext-tee-wsc-ump.git", "name": "ext-tee-wsc-ump", "path": "src", "branch": "release/2025-05-20-16-04-46"}, {"url": "***********************:weapp/wsc-tee-salesman.git", "name": "ext-tee-salesman", "path": "src", "branch": "release/2025-07-21-17-48-58"}, {"url": "***********************:fe/ranta-tee-extension-logger.git", "name": "ext-tee-logger", "path": "src", "branch": "release/1.1.3"}, {"url": "***********************:weapp/ext-tee-wsc-im.git", "name": "ext-tee-wsc-im", "path": "src", "branch": "release/2024-09-04-11-24-00"}, {"url": "***********************:weapp/ext-tee-wsc-trade.git", "name": "ext-tee-wsc-trade", "path": "src", "branch": "feature/trade-cart-perform-2025-08"}, {"url": "***********************:weapp/ext-tee-cps.git", "name": "ext-tee-cps", "path": "src", "branch": "release/1.0.0"}, {"url": "***********************:weapp/ext-tee-retail-prepaid.git", "name": "ext-tee-retail-prepaid", "path": "src", "branch": "release/1.4.39"}, {"url": "***********************:weapp/ext-tee-guide.git", "name": "ext-tee-guide", "path": "src", "branch": "release/1.4.7"}, {"url": "***********************:weapp/ext-tee-navigate.git", "name": "ext-tee-navigate", "path": "src", "branch": "release/1.1.4"}, {"url": "***********************:weapp/ext-tee-edu-goods.git", "name": "ext-tee-edu-goods", "path": "src", "branch": "release/1.2.1"}, {"url": "***********************:weapp/ext-tee-retail-groupbuy.git", "name": "ext-tee-retail-groupbuy", "path": "src", "branch": "release/1.7.0"}, {"name": "ext-tee-retail-solitaire", "path": "src", "url": "***********************:weapp/ext-tee-retail-solitaire.git", "branch": "release/1.5.0"}, {"name": "ext-tee-wholesale", "path": "src", "url": "***********************:weapp/ext-tee-wholesale.git", "branch": "release/2024-09-29-11-45-30"}, {"name": "ext-tee-common", "path": "src", "url": "***********************:weapp/ext-tee-common.git", "branch": "release/2024-03-21-16-17-50"}, {"name": "ext-tee-user", "path": "src", "url": "***********************:weapp/ext-tee-user.git", "branch": "release/2025-07-31-17-52-08"}, {"name": "ext-tee-retail-shelf", "path": "src", "url": "***********************:retail-web/retail-tee/ext-tee-retail-shelf.git", "branch": "release/2025-08-21-14-42-16"}, {"name": "ext-tee-live", "path": "src", "url": "***********************:private-live/ext-tee-live.git", "branch": "release/2025-08-28-11-49-19"}]}