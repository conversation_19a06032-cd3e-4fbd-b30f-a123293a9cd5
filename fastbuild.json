{"commands": {"pre": {"node": "v14.17.0", "yarn": "1.22.4", "dependencies": [], "saveVersionToDatabase": true, "output": ["dist/extension-map.json"]}, "publish": {"node": "v14.17.0", "yarn": "1.22.4", "dependencies": [], "saveVersionToDatabase": true, "output": ["ops-publish/version-map.json"]}, "gen-cloud-info": {"node": "v14.17.0", "yarn": "1.22.4", "dependencies": [], "saveVersionToDatabase": true}, "test-cloud": {"node": "v16.14.2", "yarn": "1.22.4", "env": ["FE_TEST_IGNORE=1", "CHROMIUM_EXECUTABLE=/usr/lib64/chromium-browser/headless_shell"], "saveVersionToDatabase": true}, "test-cloud-prod": {"node": "v16.14.2", "yarn": "1.22.4", "env": ["FE_TEST_IGNORE=1", "CHROMIUM_EXECUTABLE=/usr/lib64/chromium-browser/headless_shell"], "saveVersionToDatabase": true}, "diff-cloud": {"node": "v14.17.0", "yarn": "1.22.4", "dependencies": [], "saveVersionToDatabase": true}}}