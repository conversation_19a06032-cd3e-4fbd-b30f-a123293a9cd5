const path = require('path');
const fs = require('fs');

const rootPath = __dirname.replace(/wsc-tee-h5.*$/, '');
const weappBizPath = 'wsc/src/ranta-config/bizs/goods-detail/index.page.json';
const h5BizPath = 'wsc-tee-h5/src/ranta-config/bizs/goods-detail/0.page.json';

const weappBiz = JSON.parse(
  fs.readFileSync(path.join(rootPath, weappBizPath), 'utf-8')
);
const h5Biz = JSON.parse(
  fs.readFileSync(path.join(rootPath, h5BizPath), 'utf-8')
);

const versionToNum = (version) => {
  const arr = version.split('.');
  const mul = [1000000000000n, 100000000n, 10000n, 1n];
  if (arr.length === 3) {
    arr.push(0);
  }
  return arr.reduce((prev, curr, index) => {
    // eslint-disable-next-line no-undef
    return prev + BigInt(curr) * mul[index];
  }, 0n);
};

const cmpVer = (v1, v2) => {
  return versionToNum(v1) > versionToNum(v2);
};

const updateVerMap = (biz, extMap = {}) => {
  return biz.modules.reduce((prev, curr) => {
    const v1 = curr.extensionVersion;
    const v2 = extMap[curr.extensionName] || '0.0.0.0';
    prev[curr.extensionName] = cmpVer(v1, v2) ? v1 : v2;
    return prev;
  }, {});
};

let extMap = {};
extMap = updateVerMap(weappBiz, extMap);
extMap = updateVerMap(h5Biz, extMap);

console.log(extMap);

const updateVersion = (biz) => {
  biz.modules.forEach((item) => {
    item.extensionVersion = extMap[item.extensionName];
  });
  return biz;
};

fs.writeFile('./weapp.json', JSON.stringify(updateVersion(weappBiz)), (err) => {
  console.log(err);
});

fs.writeFile('./h5.json', JSON.stringify(updateVersion(h5Biz)), (err) => {
  console.log(err);
});
