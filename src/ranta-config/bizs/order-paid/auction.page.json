{"id": "d0d34327-2ab4-4dbe-8f40-ad77cc50d70d", "routes": ["/wsctrade/order/payresult/auction"], "containers": [{"contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-ump/paid-auction-block~UXpzctWW", "@ext-tee-wsc-decorate/theme-color~VrJeoTqE"]}], "modules": [{"id": "@ext-tee-wsc-ump/paid-auction-block~UXpzctWW", "extensionName": "@ext-tee-wsc-ump/paid-auction-block", "extensionVersion": "0.0.2", "isRemote": false, "bindings": {"data.themeCss": {"moduleId": "@ext-tee-wsc-decorate/theme-color~VrJeoTqE", "name": "themeCSS"}}}, {"id": "@ext-tee-wsc-decorate/theme-color~VrJeoTqE", "extensionName": "@ext-tee-wsc-decorate/theme-color", "extensionVersion": "1.4.4", "isRemote": false}]}