const fs = require('fs');

// 转语义化
const jsonData = JSON.parse(fs.readFileSync('0.page.json', 'utf-8'));

const moduleMap = jsonData.modules.reduce((prev, curr) => {
  prev[curr.id] = curr.extensionName + ':@' + curr.extensionVersion;
  return prev;
}, {});

const formatContents = (contents = []) => {
  return contents.map((item) => {
    if (typeof item !== 'object' || item === null) {
      return moduleMap[item] || item;
    }
    const contents = item.contents;
    if (Array.isArray(contents)) {
      item.contents = formatContents(contents);
    }
    return item;
  });
};

jsonData.containers = formatContents(jsonData.containers);

fs.writeFile('./0.page-formated.json', JSON.stringify(jsonData), (err) => {
  console.log(err);
});

// provide alias 重名检查
const aliasMap = {};
const aliasDuplicates = [];

jsonData.modules.forEach((item) => {
  // eslint-disable-next-line guard-for-in
  for (const key in item.provide) {
    item.provide[key].forEach((provideItem) => {
      const data = {
        id: item.id,
        extensionName: item.extensionName,
        type: key,
        provide: provideItem,
      };
      // Widget是在container里配的暂不考虑冲突
      // if (moduleMap[item.id] && key === 'widget') {
      //   return;
      // }
      // 冲突alias报警
      if (aliasMap[provideItem.alias]) {
        aliasDuplicates.push([aliasMap[provideItem.alias], data]);
        return;
      }
      aliasMap[provideItem.alias] = data;
    });
  }
});

fs.writeFile(
  './0.alias-duplicated.json',
  JSON.stringify(aliasDuplicates),
  (err) => {
    console.log(err);
  }
);
