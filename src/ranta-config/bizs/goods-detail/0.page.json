{"id": "d251a77c-0335-4bdb-8880-f7e26f716a6a", "config": {}, "routes": ["index"], "containers": [{"contentType": "container", "layout": "column", "contents": [{"contentType": "module", "layout": "column", "contents": ["@ext-tee-shop/shop-cert-notice~394F9ECF", "@wsc-tee-shop/shop-top-bar~35CCCD10"]}]}, {"contentType": "container", "layout": "column", "style": {"width": "100%", "padding-bottom": "85px", "background-color": "var(--theme-page-footer-bg-color, #f7f8fa)", "background-size": "100% auto", "color": "#323233", "::-webkit-scrollbar": {"width": "1px", "height": "1px"}, "::-webkit-scrollbar-track": {"background-color": "transparent"}, "::-webkit-scrollbar-thumb": {"background-color": "transparent"}}, "contents": [{"blockName": "nav-bar-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/nav-bar-block~8BD15453"]}, {"blockName": "trade-carousel", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/trade-carousel~61CBB3DE"]}, {"blockName": "image-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/image-block~37F17F76"]}, {"blockName": "base-info-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/base-info-block~7F61B12D"]}, {"blockName": "goods-ranking-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/goods-ranking-block~08A16803"]}, {"blockName": "activity-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/activity-block~B31486F6"]}, {"blockName": "logistics-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/logistics-block~ayIXEYfQ"]}, {"blockName": "promotion-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/promotion-block~FD05A518"]}, {"blockName": "multi-store-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/multi-store-blocC3EC774E"]}, {"blockName": "group-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/group-block~B77FA8FB"]}, {"blockName": "goods-extra-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/goods-extra-block~C27D9F8C"]}, {"blockName": "trade-records-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/trade-records-block~5C220A63"]}, {"blockName": "goods-review-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/goods-review-block~513112D1"]}, {"blockName": "buyer-show-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/buyer-show-block~5C6C847F"]}, {"blockName": "shop-evaluation-entry", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/shop-evaluation-entry~B5D8A1E3"]}, {"blockName": "shop-note-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/shop-note-block~8DB78701"]}, {"blockName": "shop-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/shop-block~8022B173"]}, {"blockName": "sold-out-rec-shop-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/sold-out-rec-shop-block~75C79B48"]}, {"blockName": "shop-physical-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/shop-physical-block~184F2FC0"]}, {"blockName": "recommend-plugin-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/recommend-plugin-block~19268E3A"]}, {"blockName": "goods-detail-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/goods-detail-block~0B7AEB24"]}, {"blockName": "cps-goods-recommend", "contentType": "module", "layout": "column", "contents": ["@wsc-tee-statcenter/cps-recommend-good4868F958"]}, {"blockName": "recommend-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/recommend-block~32BB9C41"]}, {"blockName": "fixed-bottom-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/fix-bottom-block~4C36D71D"]}, {"contentType": "module", "layout": "column", "contents": ["@wsc-tee-shop/footeC1B44578"]}, {"contentType": "module", "layout": "column", "contents": ["@ext-tee-user/fast-join-sdk~BddDoliE"]}, {"blockName": "lbs-block", "contentType": "module", "layout": "column", "style": {}, "contents": []}]}, {"contentType": "container", "layout": "column", "contents": [{"contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-ump/trade-buy-ump-data~362C00C8", "@ext-tee-wsc-im/im-sdk-core~085BC728", "@ext-tee-wsc-goods/share-block~3B76A76B", "@ext-tee-wsc-goods/trade-submit-block~29BF97B3", "@wsc-tee-decorate/floating-nav~CF2155C6", "@wsc-tee-salesman/salesman-cube-block~F9FA35EA", "@ext-tee-wsc-im/imc-components~9FA18AEB", "@ext-tee-wsc-goods/popup-container~A30B8FF9", "sku-order-subpage~84750401", "@wsc-tee-salesman/salesman-share-block~BDBA2A8B", "@ext-tee-wsc-goods/goods-explain-video~DCFE2510", "@ext-tee-wsc-goods/goods-ecloud~A467747E", "@wsc-tee-trade/retail-order~xeZdJbAx", "@wsc-tee-trade/retail-order-error-info~xeZdJqAq", "@ext-tee-wsc-decorate/page-style~F70775D9", "@assets-tee-extensions/cashier~AWUQJTGI", "@ext-tee-wsc-decorate/elderly-oriented~E7586E9C", "@ext-tee-wsc-goods/combine-api-block~YEnBjXhy"]}]}, {"contentType": "module", "layout": "column", "contents": ["@wsc-tee-common/app-content-append~KLQlFFwg"], "style": {"position": "relative", "z-index": 999999}}, {"contentType": "container", "layout": "column", "contents": [{"contentType": "module", "layout": "column", "contents": ["@ext-tee-live/draggable-live-player~UIfgFgzw"]}]}], "modules": [{"id": "@ext-tee-wsc-decorate/page-style~F70775D9", "extensionName": "@ext-tee-wsc-decorate/page-style", "extensionVersion": "0.0.1", "isRemote": false, "properties": {"useAppStyleIcon": true}, "stage": "pre"}, {"id": "@wsc-tee-shop/shop-top-bar~35CCCD10", "stage": "normal", "extensionName": "@wsc-tee-shop/shop-top-bar", "extensionVersion": "1.2.7", "isRemote": false, "bindings": {"data.kdtId": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "kdtId"}, "data.offlineId": {"moduleId": "@wsc-tee-shop/multi-store~brNzIGqQ", "name": "offlineId"}}}, {"id": "@assets-tee-extensions/guarantee-detail-baB946E8F7", "stage": "pre", "extensionName": "@assets-tee-extensions/guarantee-detail-bar", "extensionVersion": "2.4.22", "isRemote": false}, {"id": "@ext-tee-wsc-goods/image-block~37F17F76", "stage": "pre", "extensionName": "@ext-tee-wsc-goods/image-block", "extensionVersion": "1.4.4", "isRemote": false, "bindings": {"data.finalBigButtons": {"moduleId": "@ext-tee-wsc-goods/fix-bottom-block~4C36D71D", "name": "bigButtons"}, "event.swipe:update": [{"moduleId": "@ext-tee-wsc-goods/base-info-block~7F61B12D", "name": "nav:update"}]}}, {"id": "@ext-tee-wsc-goods/share-block~3B76A76B", "stage": "pre", "extensionName": "@ext-tee-wsc-goods/share-block", "extensionVersion": "1.3.7", "isRemote": false, "bindings": {"data.goods": {"moduleId": "@ext-tee-wsc-goods/page-setup-block~4D54A34A", "name": "goodsBaseInfo"}, "event.share:query": [{"moduleId": "@wsc-tee-salesman/salesman-share-block~BDBA2A8B", "name": "share:query"}], "event.share:setPageShareParams": [{"moduleId": "@ext-tee-wsc-goods/goods-ecloud~A467747E", "name": "share:setPageShareParams"}, {"moduleId": "@ext-tee-wsc-goods/share-block~3B76A76B", "name": "share:setPageShareParams"}], "widget.UserAuthorize": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Main"}], "event.share:show": [{"moduleId": "@ext-tee-wsc-goods/base-info-block~7F61B12D", "name": "share:show"}, {"moduleId": "@ext-tee-wsc-goods/fix-bottom-block~4C36D71D", "name": "share:show"}, {"moduleId": "@wsc-tee-decorate/floating-nav~CF2155C6", "name": "share:show"}, {"moduleId": "@ext-tee-wsc-goods/trade-submit-block~29BF97B3", "name": "share:show"}, {"moduleId": "@ext-tee-wsc-goods/goods-ecloud~A467747E", "name": "share:show"}, {"moduleId": "@ext-tee-wsc-goods/share-block~3B76A76B", "name": "share:show"}], "process.setShareInfo": [{"moduleId": "@wsc-tee-salesman/salesman-share-block~BDBA2A8B", "name": "setShareInfo"}]}}, {"id": "@wsc-tee-salesman/salesman-cube-block~F9FA35EA", "stage": "normal", "properties": {"scenes": "goods", "getShareOpportunity": "goodsData", "guideBindSourceType": 21, "needBindRelation": false}, "extensionName": "@wsc-tee-salesman/salesman-cube-block", "extensionVersion": "1.11.1", "isRemote": false, "bindings": {"data.shareInfo": {"moduleId": "@ext-tee-wsc-goods/share-block~3B76A76B", "name": "shareData"}}}, {"id": "@ext-tee-wsc-goods/fix-bottom-block~4C36D71D", "stage": "pre", "extensionName": "@ext-tee-wsc-goods/fix-bottom-block", "extensionVersion": "1.3.6", "isRemote": false, "bindings": {"data.goodsEstimatePriceData": {"moduleId": "@ext-tee-wsc-goods/trade-submit-block~29BF97B3", "name": "estimatePriceData"}, "data.limitedEstimate": {"moduleId": "@ext-tee-wsc-goods/trade-submit-block~29BF97B3", "name": "estimatePriceData"}, "event.clickBigButton": [{"moduleId": "@ext-tee-wsc-goods/image-block~37F17F76", "name": "clickBigButton"}, {"moduleId": "@ext-tee-wsc-goods/trade-records-block~5C220A63", "name": "records-sku:show"}, {"moduleId": "@ext-tee-wsc-goods/preview-image-block~49E212DD", "name": "clickBigButton"}, {"moduleId": "@ext-tee-wsc-goods/goods-explain-video~DCFE2510", "name": "clickBigButton"}, {"moduleId": "@ext-tee-wsc-goods/goods-ecloud~A467747E", "name": "btnClick"}, {"moduleId": "@ext-tee-wsc-goods/base-info-block~7F61B12D", "name": "addGift"}], "event.onImClick": [{"moduleId": "@ext-tee-wsc-goods/goods-ecloud~A467747E", "name": "showIM"}], "process.setBottomBtns": null, "process.launchFastJoinSDK": [{"moduleId": "@ext-tee-user/fast-join-sdk~BddDoliE", "name": "launchFastJoinSDK"}]}}, {"id": "@wsc-tee-shop/shop-rest~31EA6D08", "stage": "post", "extensionName": "@wsc-tee-shop/shop-rest", "extensionVersion": "1.0.6", "isRemote": false}, {"id": "@wsc-tee-shop/footeC1B44578", "stage": "normal", "extensionName": "@wsc-tee-shop/footer", "extensionVersion": "1.5.4", "isRemote": false, "bindings": {"data.buyerId": null, "data.miniprogram": null}}, {"id": "@ext-tee-wsc-goods/trade-submit-block~29BF97B3", "stage": "normal", "extensionName": "@ext-tee-wsc-goods/trade-submit-block", "extensionVersion": "1.1.2", "isRemote": false, "bindings": {"event.sku:show": [{"moduleId": "@ext-tee-wsc-goods/fix-bottom-block~4C36D71D", "name": "sku:show"}, {"moduleId": "@ext-tee-wsc-goods/popup-container~A30B8FF9", "name": "sku:show:to-submit"}, {"moduleId": "@ext-tee-wsc-goods/goods-ecloud~A467747E", "name": "showSKU"}], "event.showNearSku": [{"moduleId": "@ext-tee-wsc-goods/group-block~B77FA8FB", "name": "sku:show"}], "event.sku:preview-click": [{"moduleId": "@ext-tee-wsc-goods/biz-sku-manage~AEE492D2", "name": "sku:preview"}, {"moduleId": "@ext-tee-wsc-goods/sku-order-core~LaXFWQzW", "name": "sku:preview"}], "event.skuSubmit:act": [{"moduleId": "@ext-tee-wsc-goods/sku-order-core~LaXFWQzW", "name": "submit:act"}], "process.setTradePostData": [{"moduleId": "@wsc-tee-salesman/salesman-recruit-goods-block~873D8442", "name": "setSalesmanTradeData"}, {"moduleId": "@assets-tee-extensions/sku-pay-ways~6F044A8D", "name": "setPayWayData"}], "process.skuUpdate": [{"moduleId": "@assets-tee-extensions/sku-pay-ways~6F044A8D", "name": "installment:update"}], "widget.SkuPayWays": [{"moduleId": "@assets-tee-extensions/sku-pay-ways~6F044A8D", "name": "Main"}], "widget.CombineGoodsBlock": [{"moduleId": "@ext-tee-wsc-goods/combine-goods-block~C17D4AD9", "name": "Widget"}], "event.sku:hide": [{"moduleId": "@ext-tee-wsc-goods/popup-container~A30B8FF9", "name": "sku:hide"}, {"moduleId": "@assets-tee-extensions/sku-pay-ways~6F044A8D", "name": "sku:hide"}], "event.submit:act": [{"moduleId": "@ext-tee-wsc-goods/fix-bottom-block~4C36D71D", "name": "submit:act"}], "widget.SkuPaidCouponIntro": [{"moduleId": "@ext-tee-wsc-goods/goods-extra-block~C27D9F8C", "name": "CouponIntro"}]}}, {"id": "@ext-tee-wsc-goods/page-setup-block~4D54A34A", "stage": "pre", "asyncInit": true, "extensionName": "@ext-tee-wsc-goods/page-setup-block", "extensionVersion": "1.7.10", "isRemote": false, "bindings": {"data.oid": {"moduleId": "@wsc-tee-shop/multi-store~brNzIGqQ", "name": "offlineId"}, "event.page-container:scrollLocked": [{"moduleId": "@ext-tee-wsc-goods/image-block~37F17F76", "name": "page-container:scrollLocked"}, {"moduleId": "@ext-tee-wsc-goods/share-block~3B76A76B", "name": "page-container:scrollLocked"}, {"moduleId": "@ext-tee-wsc-goods/trade-records-block~5C220A63", "name": "page-container:scrollLocked"}]}}, {"id": "@ext-tee-wsc-goods/multi-store-blocC3EC774E", "stage": "normal", "extensionName": "@ext-tee-wsc-goods/multi-store-block", "extensionVersion": "1.2.6", "isRemote": false}, {"id": "@ext-tee-wsc-goods/trade-carousel~61CBB3DE", "stage": "post", "extensionName": "@ext-tee-wsc-goods/trade-carousel", "extensionVersion": "1.4.0", "isRemote": false}, {"id": "@ext-tee-wsc-goods/nav-bar-block~8BD15453", "stage": "post", "extensionName": "@ext-tee-wsc-goods/nav-bar-block", "extensionVersion": "1.2.6", "isRemote": false, "bindings": {"process.getNavItemTop": [{"moduleId": "@ext-tee-wsc-goods/image-block~37F17F76", "name": "getNavItemTop"}, {"moduleId": "@ext-tee-wsc-goods/goods-detail-block~0B7AEB24", "name": "getNavItemTop"}, {"moduleId": "@ext-tee-wsc-goods/recommend-block~32BB9C41", "name": "getNavItemTop"}, {"moduleId": "@ext-tee-wsc-goods/goods-review-block~513112D1", "name": "getNavItemTop"}]}}, {"id": "@assets-tee-extensions/guarantee-components~178B71FB", "stage": "pre", "extensionName": "@assets-tee-extensions/guarantee-components", "extensionVersion": "1.2.13", "isRemote": false}, {"id": "@ext-tee-wsc-im/im-sdk-core~085BC728", "stage": "normal", "extensionName": "@ext-tee-wsc-im/im-sdk-core", "extensionVersion": "1.0.3", "isRemote": false}, {"id": "@ext-tee-wsc-im/im-message-contact~247C0077", "stage": "pre", "extensionName": "@ext-tee-wsc-im/im-message-contact", "extensionVersion": "1.4.10", "isRemote": false, "bindings": {"widget.UserAuthorize": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Main"}], "data.contact": null}}, {"id": "@wsc-tee-decorate/floating-nav~CF2155C6", "stage": "normal", "properties": {"customWish": true, "sourceFrom": "goods"}, "extensionName": "@wsc-tee-decorate/floating-nav", "extensionVersion": "1.4.27-hotfix.1", "isRemote": false, "bindings": {"event.wish:state:change": [{"moduleId": "@ext-tee-wsc-goods/popup-container~A30B8FF9", "name": "wish:change"}], "event.submit:act": [{"moduleId": "@ext-tee-wsc-goods/popup-container~A30B8FF9", "name": "submit:act"}], "data.imSourceDetail": {"moduleId": "@ext-tee-wsc-goods/goods-detail-block~0B7AEB24", "name": "extraData"}, "data.shopInfo": {"moduleId": "@ext-tee-wsc-goods/page-setup-block~4D54A34A", "name": "shopBaseInfo"}}}, {"id": "@wsc-tee-shop/fullguide~C6757C5A", "stage": "post", "extensionName": "@wsc-tee-shop/fullguide", "extensionVersion": "1.0.6", "isRemote": false, "bindings": {"data.mpAccount": {"moduleId": "@ext-tee-wsc-goods/page-setup-block~4D54A34A", "name": "shopMpInfo"}}}, {"id": "@wsc-tee-decorate/showcase-container-h5~CF299B8C", "stage": "post", "extensionName": "@wsc-tee-decorate/showcase-container-h5", "extensionVersion": "1.6.26-hotfix.2", "isRemote": false, "bindings": {"data.imSourceDetail": {"moduleId": "@ext-tee-wsc-goods/goods-detail-block~0B7AEB24", "name": "extraData"}, "data.shopInfo": {"moduleId": "@ext-tee-wsc-goods/page-setup-block~4D54A34A", "name": "shopBaseInfo"}}}, {"id": "@ext-tee-wsc-goods/biz-sku-manage~AEE492D2", "stage": "post", "extensionName": "@ext-tee-wsc-goods/biz-sku-manage", "extensionVersion": "1.4.1", "isRemote": false, "properties": {"cloudExcluded": true}, "bindings": {"event.sku:show": [{"moduleId": "@wsc-tee-decorate/showcase-container-h5~CF299B8C", "name": "sku:show"}]}}, {"id": "@wsc-tee-salesman/salesman-recruit-goods-block~873D8442", "stage": "normal", "extensionName": "@wsc-tee-salesman/salesman-recruit-goods-block", "extensionVersion": "1.0.3", "isRemote": false}, {"id": "@assets-tee-extensions/sku-pay-ways~6F044A8D", "stage": "normal", "extensionName": "@assets-tee-extensions/sku-pay-ways", "extensionVersion": "2.3.5", "isRemote": false, "bindings": {"data.sku": {"moduleId": "@ext-tee-wsc-goods/trade-submit-block~29BF97B3", "name": "sku<PERSON><PERSON>"}, "data.goods": {"moduleId": "@ext-tee-wsc-goods/page-setup-block~4D54A34A", "name": "goodsBaseInfo"}}}, {"id": "@ext-tee-assets/prior-use-block~8B2FE4C6", "stage": "post", "extensionName": "@ext-tee-assets/prior-use-block", "extensionVersion": "1.1.12", "isRemote": false, "bindings": {"lambda.checkIsNeedSyncOrderScene": null}}, {"id": "@wsc-tee-statcenter/recommend-goods~6F3EE916", "stage": "post", "properties": {"useSlot": true, "totalPageNum": 3, "useLoadMore": true}, "extensionName": "@wsc-tee-statcenter/recommend-goods", "extensionVersion": "1.6.14", "isRemote": false, "bindings": {"data.themeCSS": {"moduleId": "@ext-tee-wsc-goods/page-setup-block~4D54A34A", "name": "themeVars"}, "data.title": {"moduleId": "@ext-tee-wsc-goods/recommend-block~32BB9C41", "name": "recommendTitle"}, "data.recommendTitle": {"moduleId": "@ext-tee-wsc-goods/recommend-block~32BB9C41", "name": "recommendTitle"}}}, {"id": "@wsc-tee-statcenter/cps-recommend-good4868F958", "stage": "post", "properties": {"wrapperType": "", "style": "margin-top: 8px;display: block;"}, "extensionName": "@wsc-tee-statcenter/cps-recommend-goods", "extensionVersion": "1.4.13", "isRemote": false}, {"id": "@ext-tee-wsc-goods/preview-image-block~49E212DD", "stage": "pre", "extensionName": "@ext-tee-wsc-goods/preview-image-block", "extensionVersion": "1.1.20", "isRemote": false, "bindings": {"data.finalBigButtons": {"moduleId": "@ext-tee-wsc-goods/fix-bottom-block~4C36D71D", "name": "bigButtons"}, "event.preview-image": [{"moduleId": "@ext-tee-wsc-goods/trade-submit-block~29BF97B3", "name": "sku:preview-click"}], "widget.UserAuthorize": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Main"}]}}, {"id": "@ext-tee-guide/behalf-order-goods-back-block~0F40AA1D", "stage": "normal", "extensionName": "@ext-tee-guide/behalf-order-goods-back-block", "extensionVersion": "1.0.0", "isRemote": false}, {"id": "@wsc-tee-trade/order-pay-prompt-popup~50B73A2C", "stage": "post", "extensionName": "@wsc-tee-trade/order-pay-prompt-popup", "extensionVersion": "1.2.14", "isRemote": false, "bindings": {"event.open": [{"moduleId": "@ext-tee-wsc-goods/popup-container~A30B8FF9", "name": "ORDER_PAY_PROMPT:open"}]}}, {"id": "@ext-tee-wsc-goods/popup-container~A30B8FF9", "stage": "post", "extensionName": "@ext-tee-wsc-goods/popup-container", "extensionVersion": "0.1.4", "isRemote": false, "bindings": {"event.sku:hide-after": [{"moduleId": "@ext-tee-wsc-goods/trade-submit-block~29BF97B3", "name": "sku:hide"}], "event.btn:click": [{"moduleId": "@wsc-tee-decorate/floating-nav~CF2155C6", "name": "floating:nav:wish:show"}], "event.sku:submit": [{"moduleId": "@ext-tee-wsc-goods/trade-submit-block~29BF97B3", "name": "custom:event"}], "widget.PopContent": [{"moduleId": "@wsc-tee-shop/shop-rest~31EA6D08", "name": "Main"}, {"moduleId": "@ext-tee-wsc-goods/biz-sku-manage~AEE492D2", "name": "Main"}, {"moduleId": "@ext-tee-wsc-goods/preview-image-block~49E212DD", "name": "Main"}, {"moduleId": "@wsc-tee-trade/order-pay-prompt-popup~50B73A2C", "name": "Main"}, {"moduleId": "@wsc-tee-shop/fullguide~C6757C5A", "name": "Main"}, {"moduleId": "@ext-tee-wsc-goods/sku-order-popup~aKsboPas", "name": "Main"}], "event.page-container:scrollLocked": [{"moduleId": "@ext-tee-wsc-goods/image-block~37F17F76", "name": "page-container:scrollLocked"}], "event.sku:show": [{"moduleId": "@ext-tee-wsc-goods/trade-submit-block~29BF97B3", "name": "sku:show"}], "widget.CouponList": [{"moduleId": "@ext-tee-wsc-goods/popup-container~A30B8FF9", "name": "CouponList"}], "widget.UserAuthorizePopup": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Popup"}], "widget.ProtocolPopup": [{"moduleId": "@passport-tee/protocol~Duv5tmrK", "name": "ProtocolPopup"}], "widget.CouponItem": [{"moduleId": "@ext-tee-wsc-goods/popup-container~A30B8FF9", "name": "CouponItem"}]}}, {"id": "@ext-tee-wsc-goods/logistics-block~ayIXEYfQ", "stage": "pre", "extensionName": "@ext-tee-wsc-goods/logistics-block", "extensionVersion": "1.2.0", "isRemote": false}, {"id": "@ext-tee-wsc-goods/sku-order-core~LaXFWQzW", "extensionName": "@ext-tee-wsc-goods/sku-order-core", "stage": "post", "extensionVersion": "0.0.0", "isRemote": false, "bindings": {"data.skuGoods": {"moduleId": "@ext-tee-wsc-goods/trade-submit-block~29BF97B3", "name": "goods"}, "data.goods": {"moduleId": "@wsc-tee-trade/trade-buy-core~EWLfNGHF", "name": "goods"}, "widget.Cashier": [{"moduleId": "@assets-tee-extensions/cashier-pre~A452C3F8", "name": "SingleRow"}], "process.startPay": [{"moduleId": "@wsc-tee-trade/trade-buy-pay-view~JHstPTfN", "name": "startPay"}], "widget.CouponList": [{"moduleId": "@wsc-tee-trade/trade-buy-ump-block~zNReLwTC", "name": "CouponList"}], "widget.SelfFetchList": [{"moduleId": "@wsc-tee-trade/trade-buy-self-fetch-address~JcXvVLVu", "name": "Main"}], "widget.ContactList": [{"moduleId": "@wsc-tee-trade/trade-buy-common-popup~eS<PERSON>uxke", "name": "ContactList"}], "widget.TimePicker": [{"moduleId": "@wsc-tee-trade/trade-buy-common-popup~eS<PERSON>uxke", "name": "TimePicker"}], "widget.WxAddress": [{"moduleId": "@wsc-tee-trade/trade-buy-platform-address~287A34A1", "name": "WxAdd<PERSON>"}], "widget.WechatAddress": [{"moduleId": "@wsc-tee-trade/trade-buy-platform-address~287A34A1", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "event.triggerChangeAddressPopupShow": [{"moduleId": "@wsc-tee-trade/retail-order-error-info~xeZdJqAq", "name": "onDeliveryAddressCardClick"}], "data.kdtId": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "kdtId"}}}, {"id": "@ext-tee-wsc-ump/trade-buy-ump-data~362C00C8", "stage": "post", "extensionName": "@ext-tee-wsc-ump/trade-buy-ump-data", "extensionVersion": "0.5.5", "isRemote": false, "bindings": {"data.state": {"moduleId": "@wsc-tee-trade/trade-buy-core~EWLfNGHF", "name": "state"}, "event.setGrouponIsChecked": null}}, {"id": "@assets-tee-extensions/cashier-pre~A452C3F8", "stage": "post", "extensionName": "@assets-tee-extensions/cashier-pre", "extensionVersion": "2.3.8", "isRemote": false, "bindings": {"data.themeColors": null}}, {"id": "@wsc-tee-trade/trade-buy-address-edit~3CD08D5D", "stage": "post", "extensionName": "@wsc-tee-trade/trade-buy-address-edit", "extensionVersion": "2.1.12", "isRemote": false, "bindings": {"data.themeCSS": {"moduleId": "@ext-tee-wsc-goods/page-setup-block~4D54A34A", "name": "themeVars"}, "widget.WxAddress": [{"moduleId": "@wsc-tee-trade/trade-buy-platform-address~287A34A1", "name": "WxAdd<PERSON>"}], "widget.WechatAddress": [{"moduleId": "@wsc-tee-trade/trade-buy-platform-address~287A34A1", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "widget.AliAddress": [{"moduleId": "@wsc-tee-trade/trade-buy-platform-address~287A34A1", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "data.isDirectPushRoute": {"moduleId": "@ext-tee-wsc-goods/trade-submit-block~29BF97B3", "name": "supportSkuDirectOrder"}}}, {"id": "@wsc-tee-trade/trade-buy-address-map~6C80EBBF", "stage": "post", "extensionName": "@wsc-tee-trade/trade-buy-address-map", "extensionVersion": "1.2.13", "isRemote": false, "bindings": {"data.isDirectPushRoute": {"moduleId": "@ext-tee-wsc-goods/trade-submit-block~29BF97B3", "name": "supportSkuDirectOrder"}}}, {"id": "@wsc-tee-trade/trade-buy-address-city-page~C3971641", "stage": "post", "extensionName": "@wsc-tee-trade/trade-buy-address-city-page", "extensionVersion": "1.1.6", "isRemote": false}, {"id": "@wsc-tee-trade/trade-buy-contact-page~D820EAEE", "stage": "post", "extensionName": "@wsc-tee-trade/trade-buy-contact-page", "extensionVersion": "1.0.11", "isRemote": false, "bindings": {"process.goToH5SubPage": [{"moduleId": "@ext-tee-wsc-goods/sku-order-core~LaXFWQzW", "name": "goToOrderSubPage"}], "data.kdtId": null, "data.themeVars": null, "widget.GetPhoneButton": null}}, {"id": "@wsc-tee-decorate/jump-link-h5~32325653", "extensionName": "@wsc-tee-decorate/jump-link-h5", "extensionVersion": "1.0.0-hotfix.1", "isRemote": false}, {"id": "@ext-tee-wsc-im/imc-components~9FA18AEB", "extensionName": "@ext-tee-wsc-im/imc-components", "extensionVersion": "1.0.6", "isRemote": false}, {"id": "@ext-tee-wsc-goods/combine-goods-block~C17D4AD9", "extensionName": "@ext-tee-wsc-goods/combine-goods-block", "extensionVersion": "0.0.12", "isRemote": false}, {"id": "@ext-tee-shop/shop-cert-notice~394F9ECF", "extensionName": "@ext-tee-shop/shop-cert-notice", "extensionVersion": "1.1.3", "isRemote": false}, {"id": "@wsc-tee-salesman/salesman-share-block~BDBA2A8B", "properties": {"sst": 2}, "stage": "pre", "extensionName": "@wsc-tee-salesman/salesman-share-block", "extensionVersion": "1.0.4", "isRemote": false, "bindings": {"data.kdtId": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "kdtId"}}}, {"id": "sku-order-subpage~84750401", "stage": "post", "properties": {}, "extensionName": "sku-order-subpage", "extensionVersion": "0.0.1", "isRemote": false, "bindings": {"widget.AddressEdit": [{"moduleId": "@wsc-tee-trade/trade-buy-address-edit~3CD08D5D", "name": "Main"}], "widget.AddressMap": [{"moduleId": "@wsc-tee-trade/trade-buy-address-map~6C80EBBF", "name": "Main"}], "widget.AddressCity": [{"moduleId": "@wsc-tee-trade/trade-buy-address-city-page~C3971641", "name": "Main"}], "widget.ContactEdit": [{"moduleId": "@wsc-tee-trade/trade-buy-contact-page~D820EAEE", "name": "Main"}], "widget.selfFetchAddressCity": [{"moduleId": "@wsc-tee-trade/trade-buy-self-fetch-address-city~TgyyeZAy", "name": "Main"}]}}, {"id": "@ext-tee-wsc-goods/shop-block~8022B173", "stage": "normal", "extensionName": "@ext-tee-wsc-goods/shop-block", "extensionVersion": "1.2.22", "isRemote": false, "bindings": {"data.theme": {"moduleId": "@ext-tee-wsc-goods/page-setup-block~4D54A34A", "name": "themeColors"}, "component.MessageContactButton": null, "data.goodsPriceInfo": null}}, {"id": "@ext-tee-wsc-goods/recommend-plugin-block~19268E3A", "stage": "post", "extensionName": "@ext-tee-wsc-goods/recommend-plugin-block", "extensionVersion": "1.1.12", "isRemote": false}, {"id": "@ext-tee-wsc-goods/goods-detail-block~0B7AEB24", "stage": "post", "extensionName": "@ext-tee-wsc-goods/goods-detail-block", "extensionVersion": "1.3.1", "isRemote": false, "bindings": {"data.offlineId": {"moduleId": "@wsc-tee-shop/multi-store~brNzIGqQ", "name": "offlineId"}}}, {"id": "@ext-tee-wsc-goods/recommend-block~32BB9C41", "stage": "post", "extensionName": "@ext-tee-wsc-goods/recommend-block", "extensionVersion": "1.2.19", "isRemote": false, "bindings": {"widget.PersonalRecommend": [{"moduleId": "@wsc-tee-statcenter/recommend-goods~6F3EE916", "name": "Main"}]}}, {"id": "@ext-tee-wsc-goods/goods-review-block~513112D1", "stage": "normal", "extensionName": "@ext-tee-wsc-goods/goods-review-block", "extensionVersion": "1.4.7", "isRemote": false}, {"id": "@ext-tee-wsc-goods/buyer-show-block~5C6C847F", "stage": "post", "extensionName": "@ext-tee-wsc-goods/buyer-show-block", "extensionVersion": "1.2.3", "isRemote": false}, {"id": "@ext-tee-wsc-goods/shop-note-block~8DB78701", "stage": "post", "extensionName": "@ext-tee-wsc-goods/shop-note-block", "extensionVersion": "1.1.6", "isRemote": false}, {"id": "@ext-tee-wsc-goods/sold-out-rec-shop-block~75C79B48", "stage": "post", "extensionName": "@ext-tee-wsc-goods/sold-out-rec-shop-block", "extensionVersion": "1.1.17", "isRemote": false}, {"id": "@ext-tee-wsc-goods/shop-physical-block~184F2FC0", "stage": "normal", "extensionName": "@ext-tee-wsc-goods/shop-physical-block", "extensionVersion": "1.0.3", "isRemote": false}, {"id": "@ext-tee-wsc-goods/trade-records-block~5C220A63", "stage": "post", "extensionName": "@ext-tee-wsc-goods/trade-records-block", "extensionVersion": "1.1.1", "isRemote": false, "bindings": {"data.recordsBuyButton": {"moduleId": "@ext-tee-wsc-goods/fix-bottom-block~4C36D71D", "name": "bigButtons"}}}, {"id": "@ext-tee-wsc-goods/shop-evaluation-entry~B5D8A1E3", "extensionName": "@ext-tee-wsc-goods/shop-evaluation-entry", "extensionVersion": "0.2.1", "isRemote": false}, {"id": "@ext-tee-wsc-goods/base-info-block~7F61B12D", "stage": "pre", "extensionName": "@ext-tee-wsc-goods/base-info-block", "extensionVersion": "1.1.3", "isRemote": false, "bindings": {"widget.GuaranteeDetailBar": [{"moduleId": "@assets-tee-extensions/guarantee-detail-baB946E8F7", "name": "GuaranteeDetailBar"}], "process.launchFastJoinSDK": [{"moduleId": "@ext-tee-user/fast-join-sdk~BddDoliE", "name": "launchFastJoinSDK"}], "widget.UmpPromotion": [{"moduleId": "@ext-tee-wsc-goods/promotion-block~FD05A518", "name": "GoodsPromotion"}]}}, {"id": "@ext-tee-wsc-goods/group-block~B77FA8FB", "stage": "pre", "extensionName": "@ext-tee-wsc-goods/group-block", "extensionVersion": "0.1.2", "isRemote": false}, {"id": "@ext-tee-wsc-goods/activity-block~B31486F6", "extensionName": "@ext-tee-wsc-goods/activity-block", "extensionVersion": "0.0.5", "isRemote": false, "bindings": {"data.theme": {"moduleId": "@ext-tee-wsc-goods/page-setup-block~4D54A34A", "name": "themeColors"}}}, {"id": "@ext-tee-wsc-goods/goods-extra-block~C27D9F8C", "extensionName": "@ext-tee-wsc-goods/goods-extra-block", "extensionVersion": "0.0.4", "isRemote": false}, {"id": "@ext-tee-wsc-goods/promotion-block~FD05A518", "stage": "pre", "extensionName": "@ext-tee-wsc-goods/promotion-block", "extensionVersion": "0.0.10", "isRemote": false, "bindings": {"data.theme": {"moduleId": "@ext-tee-wsc-goods/page-setup-block~4D54A34A", "name": "themeColors"}, "data.goodsEstimatePriceData": {"moduleId": "@ext-tee-wsc-goods/trade-submit-block~29BF97B3", "name": "estimatePriceData"}}}, {"id": "@wsc-tee-trade/trade-buy-misc-pre~1348665A", "stage": "normal", "extensionName": "@wsc-tee-trade/trade-buy-misc-pre", "extensionVersion": "0.1.1", "isRemote": false, "bindings": {"event.openOrderKeep": [{"moduleId": "@ext-tee-wsc-goods/trade-submit-block~29BF97B3", "name": "ORDER_KEEP:open"}], "data.goods": {"moduleId": "@wsc-tee-trade/trade-buy-core~EWLfNGHF", "name": "goods"}, "data.shop": null, "lambda.hexToRgb": null, "widget.OrderKeepDialog": null, "widget.Price": null}}, {"id": "@ext-tee-wsc-goods/goods-explain-video~DCFE2510", "stage": "post", "extensionName": "@ext-tee-wsc-goods/goods-explain-video", "extensionVersion": "0.0.3", "isRemote": false, "bindings": {"data.themeCSS": {"moduleId": "@ext-tee-wsc-decorate/theme-color~27F5C2B6", "name": "themeCSS"}, "lambda.checkIsWxvideoLive": null}}, {"id": "@ext-tee-wsc-goods/goods-ecloud~A467747E", "extensionName": "@ext-tee-wsc-goods/goods-ecloud", "extensionVersion": "0.0.0", "isRemote": false, "bindings": {"process.showServiceBar": [{"moduleId": "@ext-tee-wsc-goods/group-block~B77FA8FB", "name": "setShowServiceBar"}], "process.showPromotionList": [{"moduleId": "@ext-tee-wsc-goods/promotion-block~FD05A518", "name": "setShowGoodsPromotion"}], "process.showGuaranteeBar": [{"moduleId": "@ext-tee-wsc-goods/base-info-block~7F61B12D", "name": "setShowGuaranteeBar"}], "process.showGoodsPromotion": [{"moduleId": "@ext-tee-wsc-goods/promotion-block~FD05A518", "name": "setShowGoodsPromotion"}]}}, {"id": "@wsc-tee-trade/trade-buy-platform-address~287A34A1", "extensionName": "@wsc-tee-trade/trade-buy-platform-address", "extensionVersion": "1.0.5", "isRemote": false}, {"id": "@ext-tee-user/fast-join-sdk~BddDoliE", "extensionName": "@ext-tee-user/fast-join-sdk", "extensionVersion": "0.0.0", "isRemote": false}, {"id": "@ext-tee-wsc-decorate/theme-color~27F5C2B6", "extensionName": "@ext-tee-wsc-decorate/theme-color", "extensionVersion": "1.4.4", "isRemote": false, "stage": "pre"}, {"id": "trade-buy-privacy-bill~52F34443", "extensionName": "trade-buy-privacy-bill", "extensionVersion": "0.0.1", "isRemote": false}, {"id": "@wsc-tee-trade/trade-buy-core~EWLfNGHF", "extensionName": "@wsc-tee-trade/trade-buy-core", "extensionVersion": "0.0.0", "isRemote": false, "bindings": {"process.genCreateParams": [{"moduleId": "@ext-tee-wsc-goods/sku-order-core~LaXFWQzW", "name": "modifyCreateOrderOrderMark"}], "process.genConfirmParams": [{"moduleId": "@ext-tee-wsc-goods/sku-order-core~LaXFWQzW", "name": "modifyCreateOrderOrderMark"}, {"moduleId": "@wsc-tee-trade/retail-order~xeZdJbAx", "name": "callHookBeforeFetchShow"}, {"moduleId": "@wsc-tee-trade/retail-order-error-info~xeZdJqAq", "name": "callHookGenConfirmParams"}], "process.handleAfterCreateOrderParallel": [{"moduleId": "@wsc-tee-trade/trade-buy-pay-view~JHstPTfN", "name": "handlePayAfterCreateOrder"}], "process.hook:beforeCreateOrder": [{"moduleId": "@wsc-tee-trade/retail-order~xeZdJbAx", "name": "validateSelfFetchShop"}, {"moduleId": "@wsc-tee-trade/trade-buy-core~EWLfNGHF", "name": "handleRechargePay"}], "process.hook:mutateStateAfterFetch": [{"moduleId": "@wsc-tee-trade/retail-order-error-info~xeZdJqAq", "name": "callHookAfterFetchState"}], "process.hook:afterFetchShow": [{"moduleId": "@ext-tee-wsc-goods/sku-order-core~LaXFWQzW", "name": "afterFetchShow"}], "process.hook:afterFetchShowError": [{"moduleId": "@ext-tee-wsc-goods/sku-order-core~LaXFWQzW", "name": "afterFetchShowError"}], "data.shopInfo": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "shop"}}}, {"id": "@wsc-tee-trade/trade-buy-ump-block~zNReLwTC", "extensionName": "@wsc-tee-trade/trade-buy-ump-block", "extensionVersion": "1.0.7", "isRemote": false, "stage": "post", "bindings": {"data.goods": {"moduleId": "@wsc-tee-trade/trade-buy-core~EWLfNGHF", "name": "goods"}, "data.fansBenefit": {"moduleId": "@wsc-tee-trade/trade-buy-core~EWLfNGHF", "name": "fansBenefit"}, "widget.CouponItem": [{"moduleId": "@wsc-tee-trade/trade-buy-ump-block~zNReLwTC", "name": "CouponItem"}], "widget.UserAuthorize": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Main"}]}}, {"id": "@retail-tee-prepaid/prepaid~zLnHExuP", "extensionName": "@retail-tee-prepaid/prepaid", "extensionVersion": "0.0.4", "stage": "post", "isRemote": false, "bindings": {"event.cashier:pay:prepaid:success": [{"moduleId": "@assets-tee-extensions/cashier~AWUQJTGI", "name": "cashier:pay:success"}], "event.cashier:pay:prepaid:fail": [{"moduleId": "@assets-tee-extensions/cashier~AWUQJTGI", "name": "cashier:pay:fail"}], "event.cashier:pay:prepaid:close": [{"moduleId": "@assets-tee-extensions/cashier~AWUQJTGI", "name": "cashier:close"}], "process.prepaidStartPay": [{"moduleId": "@assets-tee-extensions/cashier~AWUQJTGI", "name": "startPay"}]}}, {"id": "@assets-tee-extensions/cashier~AWUQJTGI", "extensionName": "@assets-tee-extensions/cashier", "extensionVersion": "2.6.14", "stage": "post", "isRemote": false}, {"id": "@wsc-tee-trade/trade-buy-pay-view~JHstPTfN", "extensionName": "@wsc-tee-trade/trade-buy-pay-view", "extensionVersion": "3.0.29", "stage": "post", "isRemote": false, "bindings": {"data.goods": {"moduleId": "@wsc-tee-trade/trade-buy-core~EWLfNGHF", "name": "goods"}, "data.periodBuy": null, "event.preCashier:pay:success": [{"moduleId": "@assets-tee-extensions/cashier-pre~A452C3F8", "name": "cashier:pay:success"}], "event.preCashier:pay:fail": [{"moduleId": "@assets-tee-extensions/cashier-pre~A452C3F8", "name": "cashier:pay:fail"}], "event.cashier:pay:success": [{"moduleId": "@assets-tee-extensions/cashier~AWUQJTGI", "name": "cashier:pay:success"}], "event.cashier:pay:fail": [{"moduleId": "@assets-tee-extensions/cashier~AWUQJTGI", "name": "cashier:pay:fail"}], "process.startPay": [{"moduleId": "@assets-tee-extensions/cashier~AWUQJTGI", "name": "startPay"}], "process.queryPayChannels": [{"moduleId": "@assets-tee-extensions/cashier~AWUQJTGI", "name": "queryPayChannels"}], "process.doPay": [{"moduleId": "@assets-tee-extensions/cashier~AWUQJTGI", "name": "doPay"}], "process.startPay_preCashier": [{"moduleId": "@assets-tee-extensions/cashier-pre~A452C3F8", "name": "pay"}]}}, {"id": "@wsc-tee-trade/trade-buy-service-block~BkYrmNon", "extensionName": "@wsc-tee-trade/trade-buy-service-block", "extensionVersion": "1.0.5", "isRemote": false, "bindings": {"data.goods": {"moduleId": "@wsc-tee-trade/trade-buy-core~EWLfNGHF", "name": "goods"}, "data.shopConfig": {"moduleId": "@wsc-tee-trade/trade-buy-core~EWLfNGHF", "name": "shopConfig"}}}, {"id": "@ext-tee-wsc-goods/goods-ranking-block~08A16803", "extensionName": "@ext-tee-wsc-goods/goods-ranking-block", "extensionVersion": "1.0.11", "isRemote": false}, {"id": "@wsc-tee-trade/trade-buy-self-fetch-address-city~TgyyeZAy", "extensionName": "@wsc-tee-trade/trade-buy-self-fetch-address-city", "extensionVersion": "1.0.1", "isRemote": false}, {"id": "@wsc-tee-trade/trade-buy-self-fetch-address~JcXvVLVu", "extensionName": "@wsc-tee-trade/trade-buy-self-fetch-address", "extensionVersion": "1.1.0", "isRemote": false, "bindings": {"data.isDirectPushRoute": {"moduleId": "@ext-tee-wsc-goods/trade-submit-block~29BF97B3", "name": "supportSkuDirectOrder"}}}, {"id": "@wsc-tee-trade/trade-buy-common-popup~eS<PERSON>uxke", "extensionName": "@wsc-tee-trade/trade-buy-common-popup", "extensionVersion": "0.0.0", "isRemote": false, "bindings": {"data.goods": {"moduleId": "@wsc-tee-trade/trade-buy-core~EWLfNGHF", "name": "goods"}}}, {"id": "@wsc-tee-trade/trade-buy-self-fetch-address-city~kkawiiiX", "extensionName": "@wsc-tee-trade/trade-buy-self-fetch-address-city", "extensionVersion": "1.0.1", "isRemote": false}, {"id": "@ext-tee-wsc-goods/optional-goods-block~A5V6C3c8", "extensionName": "@ext-tee-wsc-goods/optional-goods-block", "extensionVersion": "1.1.2", "isRemote": false}, {"id": "@wsc-tee-statcenter/goods-showcase-block~ZgqJYurC", "extensionName": "@wsc-tee-statcenter/goods-showcase-block", "extensionVersion": "1.0.14", "isRemote": false}, {"id": "@wsc-tee-trade/trade-buy-id-card~fXKXlomR", "extensionName": "@wsc-tee-trade/trade-buy-id-card", "extensionVersion": "0.0.0", "isRemote": false, "bindings": {"data.goods": {"moduleId": "@wsc-tee-trade/trade-buy-core~EWLfNGHF", "name": "goods"}}}, {"id": "@ext-tee-wsc-goods/sku-order-popup~aKsboPas", "extensionName": "@ext-tee-wsc-goods/sku-order-popup", "extensionVersion": "0.0.0", "stage": "post", "isRemote": false}, {"id": "@wsc-tee-trade/trade-buy-common-cloud~JlpgMdRN", "extensionName": "@wsc-tee-trade/trade-buy-common-cloud", "extensionVersion": "0.0.0", "isRemote": false}, {"id": "@wsc-tee-trade/retail-order~xeZdJbAx", "stage": "normal", "extensionName": "@wsc-tee-trade/retail-order", "extensionVersion": "0.4.4", "isRemote": false, "bindings": {"data.shop": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "shop"}, "data.goods": {"moduleId": "@wsc-tee-trade/trade-buy-core~EWLfNGHF", "name": "goods"}, "data.kdtId": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "kdtId"}, "data.pickUpWay": {"moduleId": "@wsc-tee-trade/trade-buy-core~EWLfNGHF", "name": "pickUpWay"}}}, {"id": "@wsc-tee-trade/retail-order-error-info~xeZdJqAq", "extensionName": "@wsc-tee-trade/retail-order-error-info", "extensionVersion": "0.0.2-beta.0+hotfix-order-error-enter-shop", "isRemote": false, "bindings": {"data.shop": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "shop"}, "process.switchDeliveryTab": [{"moduleId": "@wsc-tee-trade/trade-buy-core~EWLfNGHF", "name": "switchAddressTab"}]}}, {"id": "@ext-tee-wsc-goods/combine-api-block~YEnBjXhy", "extensionName": "@ext-tee-wsc-goods/combine-api-block", "extensionVersion": "0.0.0", "isRemote": false}, {"id": "@ext-tee-live/draggable-live-player~UIfgFgzw", "extensionName": "@ext-tee-live/draggable-live-player", "extensionVersion": "0.0.0", "isRemote": false, "stage": "post", "properties": {"removeLiveUrlCookie": true}}, {"id": "@ext-tee-wsc-goods/cloud-item-detail-data~RhUcWvHe", "extensionName": "@ext-tee-wsc-goods/cloud-item-detail-data", "extensionVersion": "0.0.0", "isRemote": false}]}