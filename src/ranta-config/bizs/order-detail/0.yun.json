{"cloudKey": "order-detail", "extMapping": {"process": {"setLBSReady": {"moduleId": "@wsc-tee-trade/trade-buy-page-comm~nZgCBpro", "ioName": "ecloud:setLBSReady", "ioType": "process"}}, "event": {"beforeGetLocation": {"moduleId": "@wsc-tee-trade/trade-buy-page-comm~nZgCBpro", "ioName": "ecloud:beforeGetLocation", "ioType": "process"}}}, "slots": {"order-tips": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/detail-order-tips~IBKOLhHQ", "displayName": "顶部提示", "description": "顶部提示", "props": {}, "config": {}}, "order-status": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/detail-block-container~vuwDukZs", "displayName": "订单状态", "description": "订单状态", "props": {}, "config": {}}, "order-address": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/detail-block-container~jsSVbgVa", "displayName": "地址信息", "description": "包含物流配送信息， 收货地址信息 / 自提地址，海淘身份证，拼团代收信息等", "props": {}, "config": {}}, "self-fetch": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/detail-self-fetch~GZZCUEzh.SelfFetchMain", "displayName": "自提信息", "description": "自提信息", "props": {}, "config": {"isShowSelfFetch": {"type": "boolean"}}}, "edu-refund": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/edu-refund-action~BtXYBPlI", "displayName": "教育退款信息", "description": "教育退款信息", "props": {}, "config": {}}, "paid-promotion": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-ump/detail-ump-paid-promotion~cxEKvNha", "displayName": "支付有礼", "description": "支付有礼", "props": {}, "config": {}}, "virtual-code": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/detail-virtual-code~rZGLPLIS", "displayName": "虚拟商品", "description": "包含虚拟商品，知识付费送礼退款行等", "props": {}, "config": {}}, "cash-back": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-ump/ump-cash-back~EGNlkWqB", "displayName": "订单返现", "description": "订单返现", "props": {}, "config": {}}, "return-value-card": {"allowDelete": true, "allowMultiple": true, "consumer": "@retail-tee-prepaid/order-return-value-card~HgwexLMY", "displayName": "储值返现", "description": "储值返现", "props": {}, "config": {}}, "ump-banner": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-ump/trade-detail-ump-banner~sDIvWsyr", "displayName": "营销横幅", "description": "营销横幅", "props": {}, "config": {}}, "drug-user": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/detail-drug-user~xRfenIUy", "displayName": "用药人", "description": "用药人", "props": {}, "config": {}}, "welike-entry": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/detail-welike-entry~AuoPZXqm", "displayName": "大家喜欢", "description": "大家喜欢", "props": {}, "config": {}}, "goods-info": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/detail-shop~SjupTDxw", "displayName": "商品信息", "description": "商品信息", "props": {}, "config": {}}, "ecard": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/detail-block-container~gRUMJDHe", "displayName": "电子卡券", "description": "包含：电子卡券、核销店铺位置", "props": {}, "config": {}}, "service": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/detail-service~TiqKwCLL", "displayName": "订单附加信息", "description": "包含：运费险，买家留言，发票状态等", "props": {}, "config": {"isShowBuyerMsg": {"type": "boolean", "description": "是否隐藏买家留言Cell"}}}, "edu-info": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/edu-info~dAVwJWFK", "displayName": "教育商品信息", "description": "包含：课程信息，学院信息等", "props": {}, "config": {}}, "coupon-info": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/detail-coupon~yJbYROoK", "displayName": "优惠券信息", "description": "优惠券信息", "props": {}, "config": {}}, "price": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/detail-price~cDzMheBO", "displayName": "订单计价", "description": "订单计价", "props": {}, "config": {}}, "extra-fees": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/detail-price~cDzMheBO.ExtraFees", "displayName": "附加费", "description": "附加费", "props": {}, "config": {}}, "after-sale": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/detail-after-sale~TykmROMo", "displayName": "售后信息", "description": "售后信息", "props": {}, "config": {}}, "presale-step": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/detail-presale-steps~ppitWYSj", "displayName": "定金预售信息", "description": "定金预售信息", "props": {}, "config": {}}, "order-time": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/detail-time~AsrnxbPP", "displayName": "订单基础信息", "description": "包含：订单编号，创建时间等", "props": {}, "config": {}}, "order-footer": {"allowDelete": true, "allowMultiple": true, "consumer": "@assets-tee-extensions/guarantee-ensure~hvKxMgGi", "displayName": "底部落款", "description": "包含：有赞担保等信息", "props": {}, "config": {}}, "bottom-action": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/detail-block-container~CEZqCfQr", "displayName": "底部操作按钮", "description": "底部操作按钮", "props": {}, "config": {}}, "cps-goods-recommend": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-statcenter/cps-recommend-goods~ItVTefCq", "displayName": "周边好物", "description": "周边好物", "props": {}, "config": {}}, "recommend": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-statcenter/recommend-goods~SeDMovoB", "displayName": "推荐商品", "description": "推荐商品", "props": {}, "config": {}}}, "query": {"orderNo": {"type": "string", "aliasList": ["order_no"]}, "kdtId": {"type": "number", "aliasList": ["kdt_id"]}}}