{"id": "d084e6dc-f1b1-4416-be00-7db146fb3b32", "name": "physical-store", "config": {"navigationBarTitleText": "门店列表", "enablePullDownRefresh": true}, "routes": ["/wscshopcore/physical_store"], "containers": [{"contentType": "container", "layout": "column", "style": {"minHeight": "100vh", "backgroundColor": "#f7f8fa"}, "contents": [{"contentType": "module", "layout": "column", "contents": ["@ext-tee-shop/physical-search-bar~hlEqjhQj"]}, {"contentType": "module", "layout": "column", "contents": ["@ext-tee-shop/physical-location-bar~LDAOrYqq"]}, {"contentType": "module", "layout": "column", "style": {"marginTop": "12px"}, "contents": ["@ext-tee-shop/physical-store-list~fnboaygv"]}]}], "modules": [{"id": "@ext-tee-shop/physical-store-data~aFstykWH", "extensionName": "@ext-tee-shop/physical-store-data", "extensionVersion": "1.2.1", "isRemote": false, "bindings": {"process.getLocation": [{"moduleId": "@ext-tee-shop/physical-location-bar~LDAOrYqq", "name": "getLocation"}]}}, {"id": "@ext-tee-shop/physical-location-bar~LDAOrYqq", "extensionName": "@ext-tee-shop/physical-location-bar", "extensionVersion": "1.2.1", "isRemote": false}, {"id": "@ext-tee-shop/physical-search-bar~hlEqjhQj", "extensionName": "@ext-tee-shop/physical-search-bar", "extensionVersion": "1.1.1", "isRemote": false}, {"id": "@ext-tee-shop/physical-store-list~fnboaygv", "extensionName": "@ext-tee-shop/physical-store-list", "extensionVersion": "1.1.2", "isRemote": false}]}