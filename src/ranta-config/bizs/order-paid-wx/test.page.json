{"id": "496c9fd6-d5f0-4f14-a3fa-4e2a4ea3342c", "routes": ["/wsctrade/order/payresult-wx"], "containers": [{"contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/mock-receipt-base~IxgraAcE", "@wsc-tee-trade/paid-wx-page-setup~vViZCuzH", "@wsc-tee-trade/paid-wx-state~kidUnBsL", "@ext-tee-wsc-ump/paid-award-v2-block~qmWEZJLAas", "@wsc-tee-trade/paid-wx-action~uYHBZlFuas", "@wsc-tee-trade/paid-ext-holder~OcCbJeBOas", "@ext-tee-wsc-decorate/theme-color~yWmbOPRJas", "@ext-tee-wsc-ump/paid-coupon-block~KbhNUALIas", "@wsc-tee-salesman/salesman-pay-result-block~EzUhhXTeas"]}], "modules": [{"id": "@wsc-tee-trade/mock-receipt-base~IxgraAcE", "extensionName": "@wsc-tee-trade/mock-receipt-base", "extensionVersion": "0.1.0", "isRemote": false}, {"id": "@wsc-tee-trade/paid-wx-page-setup~vViZCuzH", "extensionName": "@wsc-tee-trade/paid-wx-page-setup", "extensionVersion": "0.1.0", "isRemote": false, "bindings": {"data.orderNo": {"moduleId": "@wsc-tee-trade/mock-receipt-base~IxgraAcE", "name": "outBizNo"}}}, {"id": "@wsc-tee-trade/paid-wx-state~kidUnBsL", "extensionName": "@wsc-tee-trade/paid-wx-state", "extensionVersion": "0.1.0", "isRemote": false}, {"id": "@wsc-tee-trade/paid-wx-action~uYHBZlFuas", "extensionName": "@wsc-tee-trade/paid-wx-action", "extensionVersion": "0.1.0", "isRemote": false, "bindings": {"data.orderNo": {"moduleId": "@wsc-tee-trade/mock-receipt-base~IxgraAcE", "name": "outBizNo"}, "data.themeCss": {"moduleId": "@ext-tee-wsc-decorate/theme-color~yWmbOPRJas", "name": "themeCSS"}}}, {"id": "@ext-tee-wsc-ump/paid-award-v2-block~qmWEZJLAas", "extensionName": "@ext-tee-wsc-ump/paid-award-v2-block", "extensionVersion": "0.1.8-beta.0+hotfix-paid-award-v2-fix", "isRemote": false, "bindings": {"data.orderNo": {"moduleId": "@wsc-tee-trade/mock-receipt-base~IxgraAcE", "name": "outBizNo"}}}, {"id": "@ext-tee-wsc-ump/paid-coupon-block~KbhNUALIas", "extensionName": "@ext-tee-wsc-ump/paid-coupon-block", "extensionVersion": "0.0.6", "isRemote": false, "bindings": {"data.orderNo": {"moduleId": "@wsc-tee-trade/mock-receipt-base~IxgraAcE", "name": "outBizNo"}}}, {"id": "@wsc-tee-trade/paid-ext-holder~OcCbJeBOas", "extensionName": "@wsc-tee-trade/paid-ext-holder", "extensionVersion": "1.0.1", "isRemote": false, "bindings": {"widget.FlowChangeUmp": null, "widget.GuideCouponBlock": null}}, {"id": "@wsc-tee-salesman/salesman-pay-result-block~EzUhhXTeas", "extensionName": "@wsc-tee-salesman/salesman-pay-result-block", "extensionVersion": "1.0.4", "isRemote": false, "bindings": {"data.orderNo": {"moduleId": "@wsc-tee-trade/mock-receipt-base~IxgraAcE", "name": "outBizNo"}}}, {"id": "@ext-tee-wsc-decorate/theme-color~yWmbOPRJas", "extensionName": "@ext-tee-wsc-decorate/theme-color", "extensionVersion": "1.4.4", "isRemote": false}]}