{"cloudKey": "wechat-receipt", "grayRuleKey": "order-paid-wx", "slots": {"status": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/paid-wx-state~PbIzDuWF.PaidState", "displayName": "状态", "description": "状态", "props": {}, "config": {}}, "action": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/paid-wx-action~uYHBZlFu.PaidAction", "displayName": "操作区", "description": "订单支付后的操作", "props": {}, "config": {}}, "paid-promotion": {"allowDelete": true, "allowMultiple": true, "consumer": "@ext-tee-wsc-ump/paid-award-v2-block~qmWEZJLA.PaidAward", "displayName": "支付有礼", "description": "订单支付后的奖励", "props": {}, "config": {}}}, "query": {"innerTransactionNo": {"type": "string"}}}