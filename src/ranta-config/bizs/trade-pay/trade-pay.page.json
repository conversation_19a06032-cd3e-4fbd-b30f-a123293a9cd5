{"id": "2fe889c3-5d3a-4c29-bd81-17da462d2fa7", "routes": ["/pay/wsctrade_pay"], "containers": [{"contentType": "container", "layout": "column", "contents": [{"blockName": "address-block", "contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/trade-pay-block-container~BxEraLAW", "@wsc-tee-trade/trade-pay-block-container~gomQntLv"]}, {"blockName": "goods-block", "contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/trade-pay-block-container~LUGwzhwW", "@wsc-tee-trade/trade-pay-block-container~kyCyKpPC", "@wsc-tee-trade/trade-pay-block-container~QHpkfbpt", "@wsc-tee-trade/trade-pay-block-container~lKBQCHZn"]}, {"blockName": "service-block", "contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/trade-pay-block-container~KFbnlhaM", "trade-buy-privacy-bill~rdiLtfFp", "@wsc-tee-trade/trade-pay-block-container~abrhuYHv"]}, {"blockName": "price-block", "contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/trade-pay-block-container~noRaTmyF", "trade-pay-prepaid-recharge~EHLgwBve", "@wsc-tee-trade/trade-pay-block-container~AGGpYGJv", "@assets-tee-extensions/guarantee-ensure~TbfYkDiG", "@wsc-tee-shop/footer~HWKOVVuW"]}, {"blockName": "submit-block", "contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/trade-pay-submit~QLeAxWNi"]}]}, {"contentType": "container", "layout": "column", "contents": [{"contentType": "module", "layout": "column", "contents": ["@assets-tee-extensions/cashier~mliQncsn"]}]}], "modules": [{"id": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "extensionName": "@wsc-tee-trade/trade-pay-page-setup", "extensionVersion": "1.2.11", "isRemote": false, "bindings": {"event.orderClose": [{"moduleId": "@wsc-tee-trade/trade-order-status~qulSfCzM", "name": "countdownOver"}]}}, {"id": "@ext-tee-wsc-decorate/theme-color~QUJfFTIb", "extensionName": "@ext-tee-wsc-decorate/theme-color", "extensionVersion": "1.4.4", "isRemote": false}, {"id": "@wsc-tee-trade/trade-pay-block-container~BxEraLAW", "extensionName": "@wsc-tee-trade/trade-pay-block-container", "extensionVersion": "1.0.1", "isRemote": false, "bindings": {"widget.MainContent": [{"moduleId": "@wsc-tee-trade/trade-order-status~qulSfCzM", "name": "OrderGuide"}, {"moduleId": "@wsc-tee-trade/trade-order-status~qulSfCzM", "name": "OrderStatusWrapper"}, {"moduleId": "@wsc-tee-trade/trade-pay-delivery-address~otgCrxsU", "name": "Contact"}, {"moduleId": "@wsc-tee-trade/trade-pay-delivery-address~otgCrxsU", "name": "LogisticsWrapper"}, {"moduleId": "@wsc-tee-trade/trade-pay-delivery-address~otgCrxsU", "name": "SelfFetch"}, {"moduleId": "@wsc-tee-trade/trade-pay-groupon~XkHZolai", "name": "GrouponHeaderCell"}, {"moduleId": "@wsc-tee-trade/trade-pay-delivery-address~otgCrxsU", "name": "StripeBorder"}]}}, {"id": "@wsc-tee-trade/trade-order-status~qulSfCzM", "extensionName": "@wsc-tee-trade/trade-order-status", "extensionVersion": "1.0.0", "isRemote": false, "bindings": {"data.title": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "orderStatusTitle"}, "data.desc": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "orderStatusDesc"}, "data.steps": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "orderStatusSteps"}, "data.icon": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "orderStatusIcon"}, "data.countdownInterval": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "orderStatusCountdownInterval"}, "data.isGroupon": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "isGrouponGoods"}}}, {"id": "@wsc-tee-trade/trade-pay-pre-pay-card-cell~lVUVrLgx", "data": [{"name": "valueCardPayPrice", "origin": "PAGE", "originalName": "valueCardPayPrice"}, {"name": "themeColors", "origin": "PAGE", "originalName": "themeColors"}], "stage": "pre", "extensionName": "@wsc-tee-trade/trade-pay-pre-pay-card-cell", "extensionVersion": "1.0.0", "isRemote": false}, {"id": "@wsc-tee-trade/trade-pay-delivery-address~otgCrxsU", "extensionName": "@wsc-tee-trade/trade-pay-delivery-address", "extensionVersion": "1.0.0", "isRemote": false, "bindings": {"data.showLogistics": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "addressShowLogistics"}, "data.showSelfFetch": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "addressShowSelfFetch"}, "data.currentContact": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "currentContact"}}}, {"id": "@wsc-tee-trade/trade-pay-submit~QLeAxWNi", "extensionName": "@wsc-tee-trade/trade-pay-submit", "extensionVersion": "1.1.18", "isRemote": false, "bindings": {"data.setupState": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "payState"}, "data.displayData": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "paySubmitDisplayData"}, "data.themeVars": {"moduleId": "@ext-tee-wsc-decorate/theme-color~QUJfFTIb", "name": "themeCSS"}, "event.pay:success": [{"moduleId": "@assets-tee-extensions/cashier~mliQncsn", "name": "cashier:pay:success"}, {"moduleId": "@assets-tee-extensions/cashier-pre~GCHeXZPg", "name": "cashier:pay:success"}], "event.pay:close": [{"moduleId": "@assets-tee-extensions/cashier~mliQncsn", "name": "cashier:close"}, {"moduleId": "@assets-tee-extensions/cashier~mliQncsn", "name": "cashier:pay:fail"}, {"moduleId": "@assets-tee-extensions/cashier-pre~GCHeXZPg", "name": "cashier:pay:fail"}], "data.payState": {"moduleId": "@wsc-tee-trade/trade-pay-submit~QLeAxWNi", "name": "payState"}, "process.startPay": [{"moduleId": "@wsc-tee-trade/trade-buy-pay-view~fIrajkop", "name": "startPay"}], "process.beforePrepay": [{"moduleId": "@wsc-tee-trade/xhs-order-core~roXkSZpT", "name": "beforePrepay"}, {"moduleId": "@wsc-tee-trade/trade-pay-submit~QLeAxWNi", "name": "beforePrepay"}, {"moduleId": "@wsc-tee-trade/alipay-order-core~yhTaMCiZ", "name": "beforePrepay"}]}}, {"id": "@wsc-tee-trade/trade-buy-pay-view~fIrajkop", "properties": {"test1": 0}, "extensionName": "@wsc-tee-trade/trade-buy-pay-view", "extensionVersion": "3.0.29", "isRemote": false, "bindings": {"data.state": {"moduleId": "@wsc-tee-trade/trade-pay-submit~QLeAxWNi", "name": "payState"}, "data.useBeforePayData": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "useBeforePay"}, "data.orderFinalPrice": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "finalPrice"}, "data.goods": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "formattedGoods"}, "data.isPeriodBuy": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "periodIsPeriodBuy"}, "process.startPay_preCashier": [{"moduleId": "@assets-tee-extensions/cashier-pre~GCHeXZPg", "name": "pay"}], "event.cashier:pay:success": [{"moduleId": "@assets-tee-extensions/cashier~mliQncsn", "name": "cashier:pay:success"}], "process.doPay": [{"moduleId": "@assets-tee-extensions/cashier~mliQncsn", "name": "doPay"}], "process.queryPayChannels": [{"moduleId": "@assets-tee-extensions/cashier~mliQncsn", "name": "queryPayChannels"}], "process.startPay": [{"moduleId": "@assets-tee-extensions/cashier~mliQncsn", "name": "startPay"}], "event.cashier:pay:fail": null}}, {"id": "@assets-tee-extensions/cashier~mliQncsn", "extensionName": "@assets-tee-extensions/cashier", "extensionVersion": "2.6.13", "isRemote": false, "bindings": {"process.beforePay": [{"moduleId": "@wsc-tee-trade/trade-buy-pay-view~fIrajkop", "name": "onPayItemClick"}, {"moduleId": "@wsc-tee-trade/trade-buy-pay-view~fIrajkop", "name": "handleBeforePay"}], "process.onPayItemClick": [{"moduleId": "@wsc-tee-trade/trade-buy-pay-view~fIrajkop", "name": "onPayItemClick"}], "widget.UserAuthorize": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Main"}]}}, {"id": "@assets-tee-extensions/guarantee-components~zBpEqJDw", "extensionName": "@assets-tee-extensions/guarantee-components", "extensionVersion": "1.2.13", "isRemote": false}, {"id": "@assets-tee-extensions/guarantee-freight-bar~OniAbthy", "extensionName": "@assets-tee-extensions/guarantee-freight-bar", "extensionVersion": "1.3.23", "isRemote": false, "bindings": {"data.isChoosedCard": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "guaranteeIsChoosedCard"}, "data.isOrderPage": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "guaranteeIsOrderPage"}, "data.isPrePayPage": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "guaranteeIsPrePayPage"}, "data.guaranteeOrderInfo": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "guaranteeGuaranteeOrderInfo"}, "data.freight": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "guaranteeFreight"}, "data.paddingConfig": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "guaranteePaddingConfig"}}}, {"id": "@wsc-tee-trade/trade-pay-block-container~LUGwzhwW", "extensionName": "@wsc-tee-trade/trade-pay-block-container", "extensionVersion": "1.0.1", "isRemote": false, "bindings": {"widget.MainContent": [{"moduleId": "@wsc-tee-trade/trade-pay-goods~hbHbtzTK", "name": "GoodsListWrapper"}, {"moduleId": "@wsc-tee-trade/trade-pay-goods~hbHbtzTK", "name": "GuaranteeFreightBarWrapper"}], "data.themeVars": {"moduleId": "@ext-tee-wsc-decorate/theme-color~QUJfFTIb", "name": "themeCSS"}}}, {"id": "@wsc-tee-trade/trade-pay-goods~hbHbtzTK", "extensionName": "@wsc-tee-trade/trade-pay-goods", "extensionVersion": "1.0.3", "isRemote": false, "bindings": {"data.isPreSale": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "isHotelPreSale"}, "data.hotel": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "hotelGoods"}, "data.totalPrice": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "goodsTotalPrice"}, "data.themeVars": {"moduleId": "@ext-tee-wsc-decorate/theme-color~QUJfFTIb", "name": "themeCSS"}}}, {"id": "@wsc-tee-trade/trade-pay-block-container~noRaTmyF", "extensionName": "@wsc-tee-trade/trade-pay-block-container", "extensionVersion": "1.0.1", "isRemote": false, "bindings": {"widget.MainContent": [{"moduleId": "@wsc-tee-trade/trade-pay-price~znQlRjyt", "name": "PricePanel"}], "data.themeVars": {"moduleId": "@ext-tee-wsc-decorate/theme-color~QUJfFTIb", "name": "themeCSS"}}}, {"id": "@wsc-tee-trade/trade-pay-price~znQlRjyt", "extensionName": "@wsc-tee-trade/trade-pay-price", "extensionVersion": "1.0.2", "isRemote": false, "bindings": {"data.totalPriceList": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "priceTotalPriceList"}, "data.useBeforePayData": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "useBeforePay"}}}, {"id": "@wsc-tee-trade/trade-pay-block-container~AGGpYGJv", "extensionName": "@wsc-tee-trade/trade-pay-block-container", "extensionVersion": "1.0.1", "isRemote": false, "bindings": {"widget.MainContent": [{"moduleId": "@wsc-tee-trade/trade-pay-presale~mhMmZecw", "name": "PreSaleCellWrapper"}, {"moduleId": "@wsc-tee-trade/detail-time~gOhPPNZT", "name": "Time"}]}}, {"id": "@wsc-tee-trade/detail-time~gOhPPNZT", "extensionName": "@wsc-tee-trade/detail-time", "extensionVersion": "1.5.5", "isRemote": false, "properties": {"cloudExcluded": true}, "bindings": {"data.time": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "orderTime"}, "data.kdtId": null, "data.miniprogram": null}}, {"id": "@wsc-tee-trade/detail-coupon~gkjEroGP", "extensionName": "@wsc-tee-trade/detail-coupon", "extensionVersion": "1.5.0", "isRemote": false, "properties": {"cloudExcluded": true}, "bindings": {"data.coupons": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "chosen<PERSON><PERSON><PERSON><PERSON>"}, "data.coupon": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "chosen<PERSON><PERSON><PERSON><PERSON>"}}}, {"id": "@wsc-tee-trade/trade-pay-block-container~abrhuYHv", "extensionName": "@wsc-tee-trade/trade-pay-block-container", "extensionVersion": "1.0.1", "isRemote": false, "bindings": {"widget.MainContent": [{"moduleId": "@wsc-tee-trade/detail-coupon~gkjEroGP", "name": "Coupon"}]}}, {"id": "@assets-tee-extensions/guarantee-ensure~TbfYkDiG", "properties": {"yzGuaranteeEnsureMt": 16}, "extensionName": "@assets-tee-extensions/guarantee-ensure", "extensionVersion": "1.0.13", "isRemote": false}, {"id": "@wsc-tee-shop/footer~HWKOVVuW", "extensionName": "@wsc-tee-shop/footer", "extensionVersion": "1.5.4", "isRemote": false, "bindings": {"data.isShowStoreInfo": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "footerShowStoreInfo"}, "data.buyerId": null, "data.miniprogram": null}}, {"id": "@wsc-tee-trade/trade-pay-groupon~XkHZolai", "extensionName": "@wsc-tee-trade/trade-pay-groupon", "extensionVersion": "1.0.0", "isRemote": false}, {"id": "@wsc-tee-trade/detail-shop~EBukPdVm", "properties": {"titleColor": "#969799", "valueColor": "#323233"}, "extensionName": "@wsc-tee-trade/detail-shop", "extensionVersion": "1.5.4", "isRemote": false, "bindings": {"data.order": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "periodOrder"}, "data.isSelfFetch": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "periodIsSelfFetch"}, "data.isPeriodBuy": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "periodIsPeriodBuy"}, "data.isMultiPeriodBuy": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "periodIsMultiPeriodBuy"}, "data.miniprogram": null, "process.handleUrlWithShopAutoEnter": null, "data.shopInfo": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "shop"}}}, {"id": "@wsc-tee-trade/trade-pay-idcard~EQQWJiTH", "extensionName": "@wsc-tee-trade/trade-pay-idcard", "extensionVersion": "1.2.12", "isRemote": false, "bindings": {"data.order": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "idcardOrder"}, "data.hasHaitaoGoods": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "idcardHasHaitaoGoods"}}}, {"id": "@wsc-tee-trade/trade-pay-block-container~kyCyKpPC", "extensionName": "@wsc-tee-trade/trade-pay-block-container", "extensionVersion": "1.0.1", "isRemote": false, "bindings": {"widget.MainContent": [{"moduleId": "@wsc-tee-trade/trade-pay-pre-pay-card-cell~lVUVrLgx", "name": "PrePayCardCell"}]}}, {"id": "@wsc-tee-trade/trade-pay-block-container~QHpkfbpt", "extensionName": "@wsc-tee-trade/trade-pay-block-container", "extensionVersion": "1.0.1", "isRemote": false, "bindings": {"widget.MainContent": [{"moduleId": "@wsc-tee-trade/detail-shop~EBukPdVm", "name": "Period"}]}}, {"id": "@wsc-tee-trade/trade-pay-block-container~KFbnlhaM", "extensionName": "@wsc-tee-trade/trade-pay-block-container", "extensionVersion": "1.0.1", "isRemote": false, "bindings": {"widget.MainContent": [{"moduleId": "@wsc-tee-trade/trade-pay-service~xQVkkRgh", "name": "Service"}]}}, {"id": "@wsc-tee-trade/trade-pay-service~xQVkkRgh", "stage": "pre", "properties": {}, "extensionName": "@wsc-tee-trade/trade-pay-service", "extensionVersion": "1.0.0", "isRemote": false, "bindings": {"data.showDeliveryType": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "deliveryShowDeliveryType"}, "data.showCheckDeliveryScope": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "deliveryShowCheckDeliveryScope"}, "data.showDeliveryTime": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "deliveryShowDeliveryTime"}, "data.service": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "deliveryService"}}}, {"id": "@wsc-tee-trade/trade-pay-block-container~gomQntLv", "extensionName": "@wsc-tee-trade/trade-pay-block-container", "extensionVersion": "1.0.1", "isRemote": false, "bindings": {"widget.MainContent": [{"moduleId": "@wsc-tee-trade/trade-pay-idcard~EQQWJiTH", "name": "Main"}]}}, {"id": "@wsc-tee-trade/trade-pay-presale~mhMmZecw", "extensionName": "@wsc-tee-trade/trade-pay-presale", "extensionVersion": "1.0.0", "isRemote": false, "bindings": {"data.order": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "depositOrder"}, "data.pay": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "depositPay"}, "data.showDeposit": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "depositShow"}}}, {"id": "trade-buy-cashier-wrap~aewjFxEA", "extensionName": "trade-buy-cashier-wrap", "extensionVersion": "1.0.0", "isRemote": false, "bindings": {"data.goods": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "formattedGoods"}, "widget.Cashier": [{"moduleId": "@assets-tee-extensions/cashier-pre~GCHeXZPg", "name": "CellGroup"}]}}, {"id": "@assets-tee-extensions/cashier-pre~GCHeXZPg", "extensionName": "@assets-tee-extensions/cashier-pre", "extensionVersion": "2.3.8", "isRemote": false, "bindings": {"data.amount": {"moduleId": "@wsc-tee-trade/trade-buy-pay-view~fIrajkop", "name": "orderAmount"}, "data.mobile": {"moduleId": "@wsc-tee-trade/trade-buy-pay-view~fIrajkop", "name": "buyerPhone"}, "data.traceId": {"moduleId": "@wsc-tee-trade/trade-pay-page-setup~xdIuoWjP", "name": "orderNo"}, "data.excludePayChannels": {"moduleId": "@wsc-tee-trade/trade-buy-pay-view~fIrajkop", "name": "excludePayTools"}}}, {"id": "@wsc-tee-trade/trade-pay-block-container~lKBQCHZn", "extensionName": "@wsc-tee-trade/trade-pay-block-container", "extensionVersion": "1.0.1", "isRemote": false, "bindings": {"widget.MainContent": [{"moduleId": "trade-buy-cashier-wrap~aewjFxEA", "name": "Main"}]}}, {"id": "@wsc-tee-trade/trade-buy-pay-tips~uGcHMKGC", "extensionName": "@wsc-tee-trade/trade-buy-pay-tips", "extensionVersion": "0.0.3", "isRemote": false}, {"id": "trade-buy-privacy-bill~rdiLtfFp", "extensionName": "trade-buy-privacy-bill", "extensionVersion": "0.0.1", "isRemote": false, "properties": {"isPayPage": true}}, {"id": "@wsc-tee-trade/retail-exclusive~GkzWgvbd", "extensionName": "@wsc-tee-trade/retail-exclusive", "extensionVersion": "0.0.0", "isRemote": false}, {"id": "@wsc-tee-trade/alipay-order-core~yhTaMCiZ", "extensionName": "@wsc-tee-trade/alipay-order-core", "extensionVersion": "0.0.1", "isRemote": false}, {"id": "@wsc-tee-trade/xhs-order-core~roXkSZpT", "extensionName": "@wsc-tee-trade/xhs-order-core", "extensionVersion": "0.0.1", "isRemote": false}, {"id": "trade-pay-prepaid-recharge~EHLgwBve", "extensionName": "trade-pay-prepaid-recharge", "extensionVersion": "0.0.0", "isRemote": false, "properties": {"margin": "0 0 10px 0"}}]}