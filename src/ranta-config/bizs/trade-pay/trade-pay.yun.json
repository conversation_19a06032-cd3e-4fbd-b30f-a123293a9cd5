{"cloudKey": "trade-pay", "slots": {"order-status": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-order-status~qulSfCzM.OrderStatus", "displayName": "订单状态", "description": "订单状态", "props": {}, "config": {}}, "delivery": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-pay-delivery-address~otgCrxsU.Logistics", "todo": "这里需要做调整，没有默认widget", "displayName": "配送信息", "description": "配送信息", "props": {}, "config": {}}, "goods-info": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-pay-goods~hbHbtzTK.GoodsList", "displayName": "商品信息", "description": "商品信息", "props": {}, "config": {}}, "guarantee-bar": {"allowDelete": true, "allowMultiple": true, "consumer": "@assets-tee-extensions/guarantee-freight-bar~OniAbthy.GuaranteeFreightBar", "displayName": "有赞担保", "description": "有赞担保", "props": {}, "config": {}}, "prepay-card": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-pay-block-container~kyCyKpPC.MainContent", "displayName": "余额/卡 储值卡", "description": "余额/卡 储值卡", "props": {}, "config": {}}, "period-buy": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-pay-block-container~QHpkfbpt.MainContent", "displayName": "周期购", "description": "周期购", "props": {}, "config": {}}, "cashier-pre": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-pay-block-container~lKBQCHZn.MainContent", "displayName": "前置收银台", "description": "前置收银台", "props": {}, "config": {}}, "service": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-pay-block-container~KFbnlhaM", "displayName": "订单服务", "description": "订单服务", "props": {}, "config": {}}, "privacy-bill": {"allowDelete": true, "allowMultiple": true, "consumer": "trade-buy-privacy-bill~rdiLtfFp", "displayName": "隐私协议", "description": "隐私协议", "props": {}, "config": {}}, "coupon": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-pay-block-container~abrhuYHv.MainContent", "displayName": "优惠券", "description": "优惠券", "props": {}, "config": {}}, "payment": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-pay-block-container~noRaTmyF.MainContent", "displayName": "结算信息", "description": "结算信息", "props": {}, "config": {}}, "presale": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-pay-presale~mhMmZecw.PreSaleCell", "displayName": "预售信息", "description": "预售信息", "props": {}, "config": {}}, "guarantee-footer": {"allowDelete": true, "allowMultiple": true, "consumer": "@assets-tee-extensions/guarantee-ensure~TbfYkDiG", "displayName": "底部有赞担保", "description": "底部有赞担保", "props": {}, "config": {}}, "footer": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-shop/footer~HWKOVVuW", "displayName": "页脚", "description": "页脚", "props": {}, "config": {}}, "bottom-bar": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-pay-submit~QLeAxWNi", "displayName": "底部提交订单", "description": "底部提交订单", "props": {}, "config": {}}}, "query": {"orderNo": {"type": "string", "aliasList": ["order_no"]}, "kdtId": {"type": "number", "aliasList": ["kdt_id"]}}}