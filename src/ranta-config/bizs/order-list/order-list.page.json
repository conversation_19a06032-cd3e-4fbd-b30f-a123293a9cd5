{"id": "a57f07a4-80b3-4ec9-af10-5e674445664d", "routes": ["/wsctrade/order/list"], "containers": [{"contentType": "container", "layout": "column", "style": {"position": "fixed", "top": "0px", "left": "0px", "width": "100%", "z-index": "10"}, "contents": [{"blockName": "bind-phone", "contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/order-list-block-container~top-notice"]}, {"blockName": "search-bar", "contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/order-list-block-container~search-bar"]}, {"blockName": "list-tabs", "contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/order-list-block-container~order-list-tabs"]}]}, {"contentType": "container", "layout": "column", "style": {"min-height": "100vh"}, "contents": [{"contentType": "module", "layout": "column", "contents": ["@wsc-h5-trade/order-list-core~order-list", "@ext-tee-wsc-decorate/page-style~order-list", "@wsc-tee-trade/flow-entrance-banner~order-list", "@ext-tee-live/draggable-live-player~hhwqghxA"]}, {"blockName": "list", "contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/order-list-block-container~order-list"]}, {"blockName": "empty-tip", "contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/order-list-block-container~empty-tip"]}, {"blockName": "cps-goods-recommend", "contentType": "module", "layout": "column", "contents": ["@wsc-tee-statcenter/cps-recommend-goods~order-list"]}, {"blockName": "recommend-block", "contentType": "module", "layout": "column", "contents": ["@wsc-tee-statcenter/recommend-goods~order-list"]}, {"contentType": "module", "layout": "column", "contents": ["@wsc-tee-shop/footer~order-list"]}]}], "modules": [{"id": "@wsc-tee-trade/order-list-block-container~empty-tip", "extensionName": "@wsc-tee-trade/order-list-block-container", "isRemote": false, "bindings": {"widget.Contents": [{"moduleId": "@wsc-h5-trade/order-list-core~order-list", "name": "ListEmptyTip"}]}}, {"id": "@wsc-tee-trade/order-list-block-container~search-bar", "extensionName": "@wsc-tee-trade/order-list-block-container", "bindings": {"widget.Contents": [{"moduleId": "@wsc-h5-trade/order-list-core~order-list", "name": "SearchBar"}]}, "isRemote": false}, {"id": "@wsc-tee-trade/order-list-block-container~top-notice", "extensionName": "@wsc-tee-trade/order-list-block-container", "bindings": {"widget.Contents": [{"moduleId": "@wsc-h5-trade/order-list-core~order-list", "name": "TopNotice"}]}, "isRemote": false}, {"id": "@wsc-tee-trade/order-list-block-container~order-list-tabs", "extensionName": "@wsc-tee-trade/order-list-block-container", "bindings": {"widget.Contents": [{"moduleId": "@wsc-h5-trade/order-list-core~order-list", "name": "OrderListTabs"}]}, "isRemote": false}, {"id": "@wsc-tee-trade/order-list-block-container~order-list", "extensionName": "@wsc-tee-trade/order-list-block-container", "bindings": {"widget.Contents": [{"moduleId": "@wsc-h5-trade/order-list-core~order-list", "name": "OrderListWrapper"}]}, "isRemote": false}, {"id": "@wsc-h5-trade/order-list-core~order-list", "extensionName": "@wsc-h5-trade/order-list-core", "bindings": {"widget.CommonDialog": [{"moduleId": "@wsc-h5-trade/order-list-core~order-list", "name": "OrderSharePopup"}, {"moduleId": "@wsc-h5-trade/order-list-core~order-list", "name": "OrderCancelPopup"}, {"moduleId": "@wsc-h5-trade/order-list-core~order-list", "name": "FailGoodsDialog"}, {"moduleId": "@wsc-h5-trade/order-list-core~order-list", "name": "WxvideoGuidePopup"}], "event.closeLevelupTip": [{"moduleId": "@wsc-tee-scrm/levelup-tip~order-list", "name": "close"}], "event.confirmLevelupTip": [{"moduleId": "@wsc-tee-scrm/levelup-tip~order-list", "name": "confirm"}]}, "isRemote": false}, {"id": "@wsc-tee-statcenter/cps-recommend-goods~order-list", "extensionName": "@wsc-tee-statcenter/cps-recommend-goods", "isRemote": false}, {"id": "@wsc-tee-statcenter/recommend-goods~order-list", "extensionName": "@wsc-tee-statcenter/recommend-goods", "isRemote": false, "bindings": {"data.enableShow": {"moduleId": "@wsc-h5-trade/order-list-core~order-list", "name": "showRecommend"}}}, {"id": "@wsc-tee-trade/flow-entrance-banner~order-list", "extensionName": "@wsc-tee-trade/flow-entrance-banner", "isRemote": false}, {"id": "@wsc-tee-trade/detail-welike-entry~order-list", "extensionName": "@wsc-tee-trade/detail-welike-entry", "isRemote": false}, {"id": "@wsc-tee-scrm/levelup-tip~order-list", "extensionName": "@wsc-tee-scrm/levelup-tip", "bindings": {"data.title": {"moduleId": "@wsc-h5-trade/order-list-core~order-list", "name": "levelupTipTitle"}}, "isRemote": false}, {"id": "@wsc-tee-shop/footer~order-list", "extensionName": "@wsc-tee-shop/footer", "isRemote": false}, {"id": "@ext-tee-wsc-decorate/page-style~order-list", "extensionName": "@ext-tee-wsc-decorate/page-style", "properties": {"useAppStyleIcon": true}, "isRemote": false}, {"id": "@wsc-tee-trade/retail-order-list~order-list", "extensionName": "@wsc-tee-trade/retail-order-list", "isRemote": false}, {"id": "@wsc-tee-trade/xhs-order-core~knUdPYcA", "extensionName": "@wsc-tee-trade/xhs-order-core", "extensionVersion": "0.0.1", "isRemote": false}, {"id": "@ext-tee-live/draggable-live-player~hhwqghxA", "extensionName": "@ext-tee-live/draggable-live-player", "extensionVersion": "0.0.0", "isRemote": false, "properties": {"removeLiveUrlCookie": true}}]}