{"id": "afca50ae-8963-41f4-a022-7009fa7ddfff", "routes": ["index"], "containers": [{"contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "@ext-tee-wsc-goods/share-block~cIfjdknu", "@ext-tee-wsc-goods/popup-container~zWSPzydo", "@ext-tee-wsc-goods/trade-submit-block~wJRwPACt", "sku-order-block~swYXbjZq", "@wsc-tee-trade/wxvideo-order-core~sKVtrjTU", "@ext-tee-wsc-ump/trade-buy-ump-data~BzzaDZXD", "@ext-tee-wsc-im/im-sdk-core~ExpMhhuA", "@wsc-tee-salesman/salesman-share-block~YeLxUSEa", "@ext-tee-wsc-decorate/page-style~xcmOdiDo", "@wsc-tee-trade/trade-buy-id-card~gGNQhAXE", "@ext-tee-user/fast-join-sdk~QrrYXvjM"]}, {"contentType": "module", "layout": "column", "contents": ["@wsc-tee-common/app-content-append~KLQlFFwg"], "style": {"position": "relative", "z-index": 999999}}], "name": "index", "modules": [{"id": "@assets-tee-extensions/sku-pay-ways~OOxULQHR", "extensionName": "@assets-tee-extensions/sku-pay-ways", "extensionVersion": "2.3.5", "isRemote": false, "bindings": {"data.sku": {"moduleId": "@ext-tee-wsc-goods/trade-submit-block~wJRwPACt", "name": "sku<PERSON><PERSON>"}, "data.kdtId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "kdtId"}, "data.goods": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsBaseInfo"}}}, {"id": "@wsc-tee-salesman/salesman-cube-block~RMnYMFeO", "extensionName": "@wsc-tee-salesman/salesman-cube-block", "extensionVersion": "1.11.2", "isRemote": false, "bindings": {"data.goodsPriceInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsPriceInfo"}, "data.currentActivity": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "currentActivity"}, "data.shareInfo": {"moduleId": "@ext-tee-wsc-goods/share-block~cIfjdknu", "name": "shareData"}, "data.goodsSkuData": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsSkuData"}, "data.goodsBaseInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsBaseInfo"}, "data.shopBaseInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "shopBaseInfo"}, "data.offlineId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "offlineId"}}}, {"id": "@ext-tee-wsc-goods/page-setup-block~KQVIDgsq", "extensionName": "@ext-tee-wsc-goods/page-setup-block", "extensionVersion": "1.7.10", "isRemote": false, "properties": {"unInit": true, "cloudExcluded": true}, "bindings": {"data.oid": {"moduleId": "@wsc-tee-shop/multi-store~brNzIGqQ", "name": "offlineId"}}}, {"id": "@ext-tee-wsc-goods/popup-container~zWSPzydo", "extensionName": "@ext-tee-wsc-goods/popup-container", "extensionVersion": "0.1.4", "isRemote": false, "properties": {"showMakeupOrder": false, "showGoodsShowcase": false, "showVisitGift": false, "showGoodsWish": false}, "bindings": {"data.marketing": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "marketing"}, "data.goodsMetaInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsMetaInfo"}, "data.goodsActivity": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsActivity"}, "data.umpActivity": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "umpActivity"}, "data.pageFeature": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "pageFeature"}, "data.currentActivity": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "currentActivity"}, "data.kdtId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "kdtId"}, "data.offlineId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "offlineId"}, "data.shopBaseInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "shopBaseInfo"}, "data.shopMpInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "shopMpInfo"}, "data.shopConfig": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "shopConfig"}, "data.serverStatus": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "serverStatus"}, "data.staticConfig": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "staticConfig"}, "data.multistore": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "multistore"}, "data.buyerId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "buyerId"}, "data.shopMetaInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "shopMetaInfo"}, "data.alias": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "alias"}, "widget.PopContent": [{"moduleId": "@ext-tee-wsc-goods/preview-image-block~UHJWprjZ", "name": "Main"}], "widget.UserAuthorizePopup": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Popup"}]}}, {"id": "@ext-tee-wsc-goods/share-block~cIfjdknu", "extensionName": "@ext-tee-wsc-goods/share-block", "extensionVersion": "1.3.9", "isRemote": false, "properties": {"cloudExcluded": true}, "bindings": {"widget.UserAuthorize": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Main"}], "data.seActivity": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "seActivity"}, "data.currentActivity": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "currentActivity"}, "data.goodsPriceInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsPriceInfo"}, "data.goods": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsBaseInfo"}, "data.kdtId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "kdtId"}, "data.offlineId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "offlineId"}, "data.shopMetaInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "shopMetaInfo"}, "data.env": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "env"}, "data.shopConfig": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "shopConfig"}}}, {"id": "sku-order-block~swYXbjZq", "extensionName": "sku-order-block", "extensionVersion": "1.0.35", "isRemote": false, "bindings": {"data.sku": {"moduleId": "@ext-tee-wsc-goods/trade-submit-block~wJRwPACt", "name": "sku<PERSON><PERSON>"}, "data.skuGoods": {"moduleId": "@ext-tee-wsc-goods/trade-submit-block~wJRwPACt", "name": "goods"}, "widget.CouponDialog": [{"moduleId": "@wsc-tee-trade/trade-buy-ump-block~hYmcDNZM", "name": "CouponList"}], "widget.Cashier": [{"moduleId": "@assets-tee-extensions/cashier-pre~FHeGKmQz", "name": "SingleRow"}], "data.pointsConfig": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "pointsConfig"}, "data.multiSkuDecision": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "multiSkuDecision"}, "data.goodsBaseInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsBaseInfo"}, "data.userInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "userInfo"}, "event.sku:hide": [{"moduleId": "@ext-tee-wsc-goods/trade-submit-block~wJRwPACt", "name": "sku:hide"}], "data.theme": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "themeColors"}}}, {"id": "sku-order-subpage~tGxubvqp", "extensionName": "sku-order-subpage", "extensionVersion": "0.0.1", "isRemote": false, "bindings": {"widget.AddressEdit": [{"moduleId": "@wsc-tee-trade/trade-buy-address-edit~nBYHWPlc", "name": "Main"}], "widget.AddressMap": [{"moduleId": "@wsc-tee-trade/trade-buy-address-map~vEjVUOMq", "name": "Main"}], "widget.AddressCity": [{"moduleId": "@wsc-tee-trade/trade-buy-address-city-page~WtrXTfqc", "name": "Main"}], "widget.ContactEdit": [{"moduleId": "@wsc-tee-trade/trade-buy-contact-page~VdjnoGlc", "name": "Main"}]}}, {"id": "@ext-tee-wsc-goods/trade-submit-block~wJRwPACt", "extensionName": "@ext-tee-wsc-goods/trade-submit-block", "extensionVersion": "1.1.2", "isRemote": false, "properties": {"cloudExcluded": true}, "bindings": {"data.goodsPriceInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsPriceInfo"}, "widget.SkuPayWays": [{"moduleId": "@assets-tee-extensions/sku-pay-ways~OOxULQHR", "name": "Main"}], "process.skuUpdate": [{"moduleId": "@assets-tee-extensions/sku-pay-ways~OOxULQHR", "name": "installment:update"}], "process.sku:validate": [{"moduleId": "@assets-tee-extensions/sku-pay-ways~OOxULQHR", "name": "installment:validate"}], "data.yzGuarantee": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "guaranteeData"}, "data.goodsActivity": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsActivity"}, "data.goodsMetaInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsMetaInfo"}, "data.goodsSkuData": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsSkuData"}, "data.pageFeature": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "pageFeature"}, "data.payWays": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "payWays"}, "data.distribution": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "distribution"}, "data.currentActivity": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "currentActivity"}, "data.umpActivity": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "umpActivity"}, "data.isCoupon": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "isCoupon"}, "data.goodsCombineInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsCombineInfo"}, "data.userGoodsStateParams": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "userGoodsStateParams"}, "data.multiSkuDecision": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "multiSkuDecision"}, "widget.CombineGoodsBlock": [{"moduleId": "@ext-tee-wsc-goods/combine-goods-block~QqpVHzKv", "name": "Widget"}], "data.marketing": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "marketing"}, "data.pointsConfig": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "pointsConfig"}, "data.kdtId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "kdtId"}, "data.goodsBaseInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsBaseInfo"}, "data.shopConfig": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "shopConfig"}, "data.shopBaseInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "shopBaseInfo"}, "data.buyConfig": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "buyConfig"}, "data.multistore": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "multistore"}, "data.pageSwitch": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "pageSwitch"}, "data.featureSwitch": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "featureSwitch"}, "data.shopMetaInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "shopMetaInfo"}, "data.themeColors": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "themeColors"}, "event.skuSubmit:act": [{"moduleId": "sku-order-block~swYXbjZq", "name": "submit:act"}], "event.sku:show": [{"moduleId": "@ext-tee-wsc-goods/popup-container~zWSPzydo", "name": "sku:show:to-submit"}, {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "sku:show"}], "event.submit:act": [{"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "submit:act"}], "event.sku:preview-click": [{"moduleId": "sku-order-block~swYXbjZq", "name": "sku:preview"}], "event.sku:hide": [{"moduleId": "@assets-tee-extensions/sku-pay-ways~OOxULQHR", "name": "sku:hide"}, {"moduleId": "@ext-tee-wsc-goods/popup-container~zWSPzydo", "name": "sku:hide"}], "process.setTradePostData": [{"moduleId": "@wsc-tee-salesman/salesman-recruit-goods-block~AXowYphC", "name": "setSalesmanTradeData"}, {"moduleId": "@assets-tee-extensions/sku-pay-ways~OOxULQHR", "name": "setPayWayData"}, {"moduleId": "sku-order-block~swYXbjZq", "name": "setSkuOrderData"}], "widget.SkuPaidCouponIntro": [{"moduleId": "@ext-tee-wsc-goods/goods-extra-block~AESqhAgF", "name": "CouponIntro"}], "event.sku:selected": [{"moduleId": "@ext-tee-wsc-goods/trade-submit-block~wJRwPACt", "name": "sku:selected"}]}}, {"id": "@ext-tee-wsc-im/im-message-contact~onYxZXQB", "extensionName": "@ext-tee-wsc-im/im-message-contact", "extensionVersion": "1.4.10", "isRemote": false, "bindings": {"widget.UserAuthorize": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Main"}], "data.contact": null}}, {"id": "@ext-tee-wsc-im/im-sdk-core~ExpMhhuA", "extensionName": "@ext-tee-wsc-im/im-sdk-core", "extensionVersion": "1.0.3", "isRemote": false}, {"id": "@ext-tee-wsc-im/subscription-message~bBpLsaSL", "extensionName": "@ext-tee-wsc-im/subscription-message", "extensionVersion": "1.1.8", "isRemote": false}, {"id": "@wsc-tee-trade/trade-buy-address-pre~cJgbLuAq", "extensionName": "@wsc-tee-trade/trade-buy-address-pre", "extensionVersion": "1.0.14", "isRemote": false, "properties": {"cloudExcluded": true}, "bindings": {"data.kdtId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "kdtId"}, "data.env": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "env"}, "data.periodBuy": null, "data.shop": null}}, {"id": "@wsc-tee-trade/trade-buy-ump-block~hYmcDNZM", "extensionName": "@wsc-tee-trade/trade-buy-ump-block", "extensionVersion": "1.0.7", "isRemote": false, "properties": {"cloudExcluded": true}, "bindings": {"data.kdtId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "kdtId"}, "data.themeColors": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "themeColors"}, "data.pay": {"moduleId": "sku-order-block~swYXbjZq", "name": "orderPayment"}, "data.shop": null, "data.offlineId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "offlineId"}, "event.submit:act": null}}, {"id": "@ext-tee-wsc-ump/trade-buy-ump-data~BzzaDZXD", "extensionName": "@ext-tee-wsc-ump/trade-buy-ump-data", "extensionVersion": "0.5.5", "isRemote": false, "bindings": {"data.state": {"moduleId": "sku-order-block~swYXbjZq", "name": "umpDataState"}}}, {"id": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "extensionName": "@ext-tee-wsc-goods/share-page-container", "extensionVersion": "0.0.0", "isRemote": false, "bindings": {}}, {"id": "@ext-tee-wsc-goods/side-nav~rlTxyGkS", "extensionName": "@ext-tee-wsc-goods/side-nav", "extensionVersion": "0.0.0", "isRemote": false, "bindings": {"widget.SalesmanIcon": [{"moduleId": "@wsc-tee-salesman/salesman-cube-block~RMnYMFeO", "name": "Widget"}], "data.goods": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsBaseInfo"}}}, {"id": "@ext-tee-wsc-goods/combine-goods-block~QqpVHzKv", "extensionName": "@ext-tee-wsc-goods/combine-goods-block", "extensionVersion": "0.0.13", "isRemote": false, "bindings": {"data.goodsCombineInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsCombineInfo"}}}, {"id": "@wsc-tee-trade/wxvideo-order-core~sKVtrjTU", "extensionName": "@wsc-tee-trade/wxvideo-order-core", "extensionVersion": "0.0.9", "isRemote": false, "bindings": {"data.kdtId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "kdtId"}}}, {"id": "@assets-tee-extensions/cashier-pre~FHeGKmQz", "extensionName": "@assets-tee-extensions/cashier-pre", "extensionVersion": "2.3.8", "isRemote": false, "bindings": {"data.themeColors": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "themeColors"}}}, {"id": "@ext-tee-assets/prior-use-block~uznSqtrT", "extensionName": "@ext-tee-assets/prior-use-block", "extensionVersion": "1.1.12", "isRemote": false, "bindings": {"data.goodsPriceInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsPriceInfo"}, "data.payWays": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "payWays"}, "data.umpActivity": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "umpActivity"}, "data.goodsActivity": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsActivity"}, "data.env": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "env"}, "data.goods": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsBaseInfo"}, "data.shopConfig": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "shopConfig"}, "data.goodsBaseInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsBaseInfo"}, "data.kdtId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "kdtId"}, "data.buyerId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "buyerId"}}}, {"id": "@wsc-tee-trade/trade-buy-address-edit~nBYHWPlc", "extensionName": "@wsc-tee-trade/trade-buy-address-edit", "extensionVersion": "2.1.12", "isRemote": false, "bindings": {"data.kdtId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "kdtId"}, "data.themeColors": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "themeColors"}, "data.themeCSS": {"moduleId": "@ext-tee-wsc-goods/page-setup-block~KQVIDgsq", "name": "themeVars"}}}, {"id": "@wsc-tee-trade/trade-buy-address-map~vEjVUOMq", "extensionName": "@wsc-tee-trade/trade-buy-address-map", "extensionVersion": "1.2.13", "isRemote": false, "bindings": {"data.kdtId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "kdtId"}, "data.themeColors": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "themeColors"}}}, {"id": "@wsc-tee-trade/trade-buy-address-city-page~WtrXTfqc", "extensionName": "@wsc-tee-trade/trade-buy-address-city-page", "extensionVersion": "1.1.6", "isRemote": false}, {"id": "@wsc-tee-trade/trade-buy-contact-page~VdjnoGlc", "extensionName": "@wsc-tee-trade/trade-buy-contact-page", "extensionVersion": "1.0.11", "isRemote": false, "bindings": {"data.kdtId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "kdtId"}, "widget.UserAuthorize": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Main"}], "data.themeColors": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "themeColors"}, "process.goToH5SubPage": [{"moduleId": "sku-order-block~swYXbjZq", "name": "goToOrderSubPage"}]}}, {"id": "@wsc-tee-salesman/salesman-recruit-goods-block~AXowYphC", "extensionName": "@wsc-tee-salesman/salesman-recruit-goods-block", "extensionVersion": "1.0.3", "isRemote": false}, {"id": "@wsc-tee-salesman/salesman-share-block~YeLxUSEa", "extensionName": "@wsc-tee-salesman/salesman-share-block", "extensionVersion": "1.0.4", "isRemote": false, "bindings": {"data.kdtId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "kdtId"}}, "properties": {"sst": 2}}, {"id": "@ext-tee-wsc-goods/goods-extra-block~AESqhAgF", "extensionName": "@ext-tee-wsc-goods/goods-extra-block", "extensionVersion": "0.0.4", "isRemote": false, "bindings": {"data.goodsBaseInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsBaseInfo"}, "data.goodsActivity": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsActivity"}, "data.goodsSkuData": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsSkuData"}, "data.goodsMetaInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsMetaInfo"}}}, {"id": "@ext-tee-wsc-goods/preview-image-block~UHJWprjZ", "extensionName": "@ext-tee-wsc-goods/preview-image-block", "extensionVersion": "1.1.20", "isRemote": false, "bindings": {"widget.UserAuthorize": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Main"}], "event.preview-image": [{"moduleId": "@ext-tee-wsc-goods/trade-submit-block~wJRwPACt", "name": "sku:preview-click"}, {"moduleId": "sku-order-block~swYXbjZq", "name": "sku:preview"}], "data.kdtId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "kdtId"}, "data.goodsPriceInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsPriceInfo"}, "data.goodsBaseInfo": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "goodsBaseInfo"}}}, {"id": "@ext-tee-wsc-decorate/theme-color~QASpDuxJ", "extensionName": "@ext-tee-wsc-decorate/theme-color", "extensionVersion": "1.4.4", "isRemote": false, "stage": "pre", "bindings": {"data.kdtId": {"moduleId": "@ext-tee-wsc-goods/share-page-container~jTMQqCTc", "name": "kdtId"}}}, {"id": "@ext-tee-wsc-decorate/page-style~xcmOdiDo", "extensionName": "@ext-tee-wsc-decorate/page-style", "extensionVersion": "0.0.1", "isRemote": false, "properties": {"useAppStyleIcon": true}, "stage": "pre"}, {"id": "trade-buy-privacy-bill~dLaQQyBD", "extensionName": "trade-buy-privacy-bill", "extensionVersion": "0.0.1", "isRemote": false}, {"id": "@wsc-tee-trade/trade-buy-id-card~gGNQhAXE", "extensionName": "@wsc-tee-trade/trade-buy-id-card", "extensionVersion": "0.0.0", "isRemote": false}, {"id": "@ext-tee-user/fast-join-sdk~QrrYXvjM", "extensionName": "@ext-tee-user/fast-join-sdk", "extensionVersion": "0.0.0", "isRemote": false}, {"id": "@ext-tee-wsc-goods/optional-goods-block~gPOKYRfP", "extensionName": "@ext-tee-wsc-goods/optional-goods-block", "extensionVersion": "1.1.2", "isRemote": false}]}