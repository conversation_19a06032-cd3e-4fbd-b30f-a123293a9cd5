{"cloudKey": "trade-buy", "extMapping": {"process": {"setLBSReady": {"moduleId": "@wsc-tee-trade/trade-buy-page-comm~nZgCBpro", "ioName": "ecloud:setLBSReady", "ioType": "process"}}, "event": {"beforeGetLocation": {"moduleId": "@wsc-tee-trade/trade-buy-page-comm~nZgCBpro", "ioName": "ecloud:beforeGetLocation", "ioType": "process"}}}, "slots": {"page-style": {"allowDelete": false, "allowMultiple": true, "consumer": "@ext-tee-wsc-decorate/page-style~gGpmuMgP", "displayName": "页面风格", "description": "页面风格", "props": {}, "config": {"bottomBarBoxShadow": {"type": "string", "description": "底栏阴影样式"}, "pageBgColor": {"type": "string", "description": "页面背景色"}, "footerBgColor": {"type": "string", "description": "页脚背景色"}, "cardMarginLeft": {"type": "string", "description": "卡片左外边距"}, "cardMarginRight": {"type": "string", "description": "卡片右外边距"}, "cardMarginTop": {"type": "string", "description": "卡片上外边距"}, "cardMarginBottom": {"type": "string", "description": "卡片下外边距"}, "cardPaddingLeft": {"type": "string", "description": "卡片左内边距"}, "cardPaddingRight": {"type": "string", "description": "卡片右内边距"}, "cardPaddingTop": {"type": "string", "description": "卡片上内边距"}, "cardPaddingBottom": {"type": "string", "description": "卡片下内边距"}, "cellPaddingLeft": {"type": "string", "description": "Cell左内边距"}, "cellPaddingRight": {"type": "string", "description": "Cell右内边距"}, "cellPaddingTop": {"type": "string", "description": "Cell上内边距"}, "cellPaddingBottom": {"type": "string", "description": "Cell下内边距"}, "tabbarPaddingTop": {"type": "string", "description": "TabBar(例如地址tabbar)的上内间距"}, "tabbarPaddingRight": {"type": "string", "description": "TabBar(例如地址tabbar)的右内间距"}, "tabbarPaddingBottom": {"type": "string", "description": "TabBar(例如地址tabbar)的下内间距"}, "tabbarPaddingLeft": {"type": "string", "description": "TabBar(例如地址tabbar)的左内间距"}, "tabRadiusTopLeft": {"type": "string", "description": "Tab(例如地址tab，发票类型等)的左上圆角"}, "tabRadiusTopRight": {"type": "string", "description": "Tab(例如地址tab，发票类型等)的右上圆角"}, "tabRadiusBottomLeft": {"type": "string", "description": "Tab(例如地址tab，发票类型等)的左下圆角"}, "tabRadiusBottomRight": {"type": "string", "description": "Tab(例如地址tab，发票类型等)的右下圆角"}, "tabMarginRight": {"type": "string", "description": "Tab(例如地址tab，发票类型等)的右间距"}, "tabBackgroundColor": {"type": "string", "description": "Tab(例如地址tab，发票类型等)的背景色"}, "tabBorderColor": {"type": "string", "description": "Tab(例如地址tab，发票类型等)的边框色"}, "tabColor": {"type": "string", "description": "Tab(例如地址tab，发票类型等)的字体色"}, "tabBackgroundColorActive": {"type": "string", "description": "Tab(例如地址tab，发票类型等)的选中背景色"}, "tabBorderColorActive": {"type": "string", "description": "Tab(例如地址tab，发票类型等)的选中边框色"}, "tabColorActive": {"type": "string", "description": "Tab(例如地址tab，发票类型等)的选中字体色"}, "tabBackgroundColorDisabled": {"type": "string", "description": "Tab(例如地址tab，发票类型等)的禁用背景色"}, "tabBorderColorDisabled": {"type": "string", "description": "Tab(例如地址tab，发票类型等)的禁用边框色"}, "tabColorDisabled": {"type": "string", "description": "Tab(例如地址tab，发票类型等)的禁用字体色"}, "tabGroupBackgroundColor": {"type": "string", "description": "Tab组(例如地址tab，发票类型等)的背景色"}, "stepperInputMarginLeft": {"type": "string", "description": "Stepper(例如商品数量等)的输入框左外间距"}, "stepperInputMarginRight": {"type": "string", "description": "Stepper(例如商品数量等)的输入框右外间距"}, "stepperBorderColor": {"type": "string", "description": "Stepper(例如商品数量等)的边框色"}, "stepperBackgroundColor": {"type": "string", "description": "Stepper(例如商品数量等)的背景色"}, "stepperBorderTopRightRadius": {"type": "string", "description": "Stepper(例如商品数量等)的边框右上圆角"}, "stepperBorderTopLeftRadius": {"type": "string", "description": "Stepper(例如商品数量等)的边框左上圆角"}, "stepperBorderBottomLeftRadius": {"type": "string", "description": "Stepper(例如商品数量等)的边框左下圆角"}, "stepperBorderBottomRightRadius": {"type": "string", "description": "Stepper(例如商品数量等)的边框右下圆角"}, "stepperColorDisabled": {"type": "string", "description": "Stepper(例如商品数量等)的禁用字体色"}, "dialogBackgroundColor": {"type": "string", "description": "Dialog弹窗的背景色"}}}, "guide": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-header-container~sXEkDyzO.Guide", "displayName": "下单引导", "description": "下单引导", "props": {}, "config": {}}, "delivery": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-address-pre~KwliftbD.DeliveryBlock", "displayName": "配送信息", "description": "配送信息", "props": {}, "config": {"addressTipsBackgroundColor": {"type": "string", "description": "地址提示背景色"}, "addressTipsPadding": {"type": "string", "description": "地址提示内间距"}}}, "logistics": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-address-pre~KwliftbD.DeliveryBlock", "displayName": "配送信息", "description": "配送信息", "props": {}, "config": {"addressTipsBackgroundColor": {"type": "string", "description": "地址提示背景色"}, "addressTipsPadding": {"type": "string", "description": "地址提示内间距"}}}, "address-list-item": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-address-pre~KwliftbD.AddressListItem", "displayName": "选择地址列表弹窗的地址Item", "description": "选择地址列表弹窗的地址Item", "props": {}, "config": {}}, "drug-user": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-drug~vmOvqvMl", "displayName": "用药人信息", "description": "用药人信息", "props": {}, "config": {}}, "goods-info": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-goods~WlUoDiBm.GoodsBlock", "displayName": "商品信息", "description": "商品信息", "props": {}, "config": {}}, "item-info": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-goods~WlUoDiBm.GoodsBlock", "displayName": "商品信息", "description": "商品信息", "props": {}, "config": {}}, "item-info-num": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-goods~WlUoDiBm.GoodNum", "displayName": "商品信息", "description": "商品信息", "parentSlot": "item-info", "props": {"num": {"type": "number", "description": "商品数量"}}, "config": {}}, "goods-empty": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-goods~WlUoDiBm.Empty", "displayName": "无可购买商品信息", "description": "无可购买商品信息", "props": {}, "config": {}}, "payment": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-ump-wrapper~ZXtnVvry", "displayName": "营销信息", "description": "营销信息", "props": {}, "config": {}}, "payment-base-info": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-ump-block~jOQaHusv.BaseBlock", "displayName": "基本结算信息", "description": "基本结算信息", "props": {}, "config": {}}, "extra-fees": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-ump-block~jOQaHusv.ExtraFees", "displayName": "附加费", "description": "附加费", "props": {}, "config": {}}, "plus-buy": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-ump-block~jOQaHusv.PlusBuy", "displayName": "加价购", "description": "加价购", "props": {}, "config": {}}, "guarantee-bar": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-goods~WlUoDiBm.GuaranteeFreightBar", "displayName": "有赞担保", "description": "有赞担保", "props": {}, "config": {}}, "customer-card": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-ump-block~jOQaHusv.MembershipCell", "displayName": "会员优惠", "description": "会员优惠", "props": {}, "config": {}}, "coupon": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-ump-block~jOQaHusv.CouponCell", "displayName": "优惠券", "description": "优惠券", "props": {}, "config": {}}, "activity": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-ump-block~jOQaHusv.ActivityCell", "displayName": "活动优惠", "description": "活动优惠", "props": {}, "config": {}}, "point": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-ump-block~jOQaHusv.PointCell", "displayName": "积分商城", "description": "积分商城", "props": {}, "config": {}}, "point-deduction": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-ump-block~jOQaHusv.PointDeductionCell", "displayName": "积分抵扣", "description": "积分抵扣", "props": {}, "config": {}}, "prepay": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-ump-block~jOQaHusv.PrepayCardCell", "displayName": "余额/卡 储值卡", "description": "余额/卡 储值卡", "props": {}, "config": {}}, "cashier-pre": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-cashier-pre~RcWwXrKB.CashierPre", "displayName": "前置收银台", "description": "前置收银台(widget级)", "props": {}, "config": {}}, "service": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-service-wrapper~hFzqwxxK", "displayName": "服务信息", "description": "服务信息", "props": {}, "config": {}}, "invoice": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-service-block~KOODDfWl.InvoiceCell", "displayName": "发票", "description": "发票", "props": {}, "config": {}}, "buyer-msg": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-service-block~KOODDfWl.BuyerMsg", "displayName": "买家留言", "description": "买家留言(widget级)", "props": {}, "config": {}}, "installment": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-block-container~wKmgVdYi", "displayName": "分期付款", "description": "分期付款", "props": {}, "config": {}}, "presale": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-price~ebCNDGHK.Presale", "displayName": "预售信息", "description": "预售信息", "props": {}, "config": {}}, "coupon-list-popup-bottom": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-ump-block~jOQaHusv.CouponListPopupBottom", "displayName": "营销信息优惠券弹窗按钮", "description": "营销信息优惠券弹窗按钮插槽", "props": {}, "config": {}}, "afterpay": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-prior-use~QaLWscbf", "displayName": "先用后付", "description": "先用后付", "props": {}, "config": {}}, "bottom-bar": {"allowDelete": true, "allowMultiple": true, "consumer": "@wsc-tee-trade/trade-buy-submit~iyvPfGjL.BottomSubmit", "displayName": "底部提交订单bar", "description": "底部提交订单bar", "props": {}, "config": {}}}, "query": {"bookKey": {"type": "string", "aliasList": ["book_key"]}, "addressId": {"type": "number", "aliasList": ["address_id"]}, "kdtId": {"type": "number", "aliasList": ["kdt_id"]}}}