{"id": "a82f2393-73e7-464e-a555-f53ac5572293", "routes": ["/pay/wsctrade_buy"], "config": {"navigationBarTitleText": "确认订单"}, "containers": [{"contentType": "container", "layout": "column", "style": {}, "contents": [{"contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/trade-buy-core~CNblvZOR", "@ext-tee-shop/shop-cert-notice~NzIIzmlO", "@ext-tee-wsc-decorate/elderly-oriented~E7586E9C"], "style": {"width": "100%"}}, {"contentType": "module", "layout": "column", "style": {}, "contents": ["@wsc-tee-trade/trade-buy-header-container~sXEkDyzO"]}, {"blockName": "event-block", "contentType": "module", "layout": "column", "style": {"position": "relative"}, "contents": ["@wsc-tee-trade/retail-order-reserves~JqvNVwQz"]}, {"blockName": "address-block", "contentType": "module", "layout": "column", "style": {"position": "relative"}, "contents": ["@wsc-tee-trade/trade-buy-address-pre~KwliftbD", "@wsc-tee-trade/trade-buy-drug~vmOvqvMl"]}, {"blockName": "goods-block", "contentType": "module", "layout": "column", "style": {}, "contents": ["@wsc-tee-trade/trade-buy-goods~WlUoDiBm"]}, {"blockName": "ump-block", "contentType": "module", "layout": "column", "style": {}, "contents": ["@ext-tee-wsc-ump/trade-buy-ump-data~pZmVaTcf", "@wsc-tee-trade/trade-buy-ump-wrapper~ZXtnVvry"]}, {"desc": "前置收银台 - wrap", "contentType": "module", "layout": "column", "style": {}, "contents": ["@wsc-tee-trade/trade-buy-cashier-pre~RcWwXrKB"]}, {"blockName": "service-block", "contentType": "module", "layout": "column", "style": {}, "contents": ["@wsc-tee-trade/trade-buy-service-wrapper~hFzqwxxK"]}, {"contentType": "module", "layout": "column", "contents": ["trade-buy-privacy-bill~UkvefXpO"]}, {"blockName": "installment-block", "desc": "分期支付，应该放在submit下面", "contentType": "module", "layout": "column", "style": {}, "contents": ["@wsc-tee-trade/trade-buy-block-container~wKmgVdYi"]}, {"blockName": "price-block", "contentType": "module", "layout": "column", "style": {}, "contents": ["@wsc-tee-trade/trade-buy-price~ebCNDGHK"]}, {"blockName": "submit-block", "contentType": "module", "layout": "column", "style": {}, "contents": ["@wsc-tee-trade/trade-buy-prior-use~QaLWscbf", "@wsc-tee-trade/trade-buy-submit~iyvPfGjL"]}, {"contentType": "module", "layout": "column", "style": {}, "contents": ["@wsc-tee-trade/trade-buy-block-container~jjshYoHO", "@assets-tee-extensions/guarantee-ensure~dLcrLIXO", "@wsc-tee-trade/trade-buy-reward-popup~bjtTCsFN", "@wsc-tee-trade/cart-present-popup~cvTYLOAY", "@assets-tee-extensions/cashier~dWXpmGYo", "@passport-tee/user-authorize~FHzemhCn", "@assets-tee-extensions/cashier~RsgazGAT", "@wsc-tee-trade/trade-buy-misc~qbcwTCHI", "@wsc-tee-trade/trade-buy-popup-container~aJKfcGYu", "@ext-tee-wsc-decorate/page-style~gGpmuMgP", "@wsc-tee-shop/footer~BXYpISCd", "@wsc-tee-trade/retail-order-error-info~hzjErGEf", "@ext-tee-wsc-goods/base-common-sku~zCdvMfGd"]}, {"blockName": "lbs-block", "contentType": "module", "layout": "column", "style": {}, "contents": []}, {"contentType": "module", "layout": "column", "contents": ["@ext-tee-user/fast-join-sdk~XvGJdnqz"]}]}], "modules": [{"id": "@wsc-tee-trade/retail-order-reserves~JqvNVwQz", "extensionName": "@wsc-tee-trade/retail-order-reserves", "extensionVersion": "1.0.0", "isRemote": false}, {"id": "@wsc-tee-trade/trade-buy-core~CNblvZOR", "extensionName": "@wsc-tee-trade/trade-buy-core", "extensionVersion": "0.0.0", "isRemote": false, "properties": {"source": "buy"}, "bindings": {"process.genCreateParams": [{"moduleId": "@wsc-tee-trade/trade-buy-page-setup~lyUjPNSI", "name": "changeCreateOrderParamsSource"}, {"moduleId": "@wsc-tee-trade/trade-buy-page-setup~lyUjPNSI", "name": "changeCreateOrderParamsExt"}, {"moduleId": "@wsc-tee-trade/trade-buy-submit~iyvPfGjL", "name": "updateCreateParamsOnPreCashierPay"}, {"moduleId": "@wsc-tee-trade/retail-order~wordavPC", "name": "callHookBeforeCreateOrder"}, {"moduleId": "@wsc-tee-trade/trade-buy-page-setup~lyUjPNSI", "name": "changeCreateOrderParamByCloud"}, {"moduleId": "@wsc-tee-trade/xhs-order-core~igNxLRdK", "name": "changeXhsLocalLifeCreateOrderParam"}, {"moduleId": "@ext-tee-guide/behalf-order-trade-buy-block~hxjmcfJK", "name": "addGuideOrderExt"}], "process.genConfirmParams": [{"moduleId": "@wsc-tee-trade/trade-buy-page-setup~lyUjPNSI", "name": "changeCreateOrderParamsSource"}, {"moduleId": "@wsc-tee-trade/retail-order~wordavPC", "name": "callHookBeforeFetchShow"}, {"moduleId": "@wsc-tee-trade/retail-order-error-info~hzjErGEf", "name": "callHookGenConfirmParams"}], "process.hook:afterPrepareByBookKeyError": [{"moduleId": "@wsc-tee-trade/trade-buy-page-setup~lyUjPNSI", "name": "handlePrepareDefaultError"}], "process.hook:beforeFetchShow": [{"moduleId": "@wsc-tee-trade/trade-buy-page-setup~lyUjPNSI", "name": "handleBeforeFetchShow"}], "process.hook:afterFetchShow": [{"moduleId": "@ext-tee-wsc-ump/trade-buy-ump-data~pZmVaTcf", "name": "fetchUmpPlusBuyProcess"}], "process.hook:beforeCreateOrder": [{"moduleId": "@wsc-tee-trade/trade-buy-address-pre~KwliftbD", "name": "validateSelfFetch"}, {"moduleId": "@wsc-tee-trade/trade-buy-goods~WlUoDiBm", "name": "validateHotel"}, {"moduleId": "@wsc-tee-trade/trade-buy-price~ebCNDGHK", "name": "handlePresaleConfirm"}, {"moduleId": "@ext-tee-wsc-ump/trade-buy-ump-data~pZmVaTcf", "name": "handleExternalCoupon"}, {"moduleId": "@wsc-tee-trade/trade-buy-drug~vmOvqvMl", "name": "showYiYaoBuyDialog"}, {"moduleId": "@retail-tee-prepaid/prepaid~SeJOhfBK", "name": "validateRechargeRetention"}, {"moduleId": "@wsc-tee-trade/trade-buy-page-setup~lyUjPNSI", "name": "handleBeforeCreateOrder"}, {"moduleId": "@wsc-tee-trade/retail-order~wordavPC", "name": "validateSelfFetchShop"}, {"moduleId": "@wsc-tee-trade/trade-buy-core~CNblvZOR", "name": "handleRechargePay"}, {"moduleId": "@wsc-tee-trade/trade-buy-address-pre~KwliftbD", "name": "addressConfirm"}], "process.hook:beforeUpdateAddress": [{"moduleId": "@wsc-tee-trade/retail-order~wordavPC", "name": "callHookBeforeUpdateAddress"}], "process.hook:mutateStateAfterFetch": [{"moduleId": "@wsc-tee-trade/trade-buy-misc~qbcwTCHI", "name": "blindBoxProcess"}, {"moduleId": "@ext-tee-guide/behalf-order-trade-buy-block~hxjmcfJK", "name": "setGuestAddress"}, {"moduleId": "@wsc-tee-trade/retail-order~wordavPC", "name": "callHookAfterFetchShow"}, {"moduleId": "@wsc-tee-trade/retail-order-error-info~hzjErGEf", "name": "callHookAfterFetchState"}, {"moduleId": "@wsc-tee-trade/trade-buy-address-pre~KwliftbD", "name": "setOrderTimeCache"}], "process.handleAfterCreateOrderParallel": [{"moduleId": "@wsc-tee-trade/trade-buy-pay-view~EnqbLHqh", "name": "handlePayAfterCreateOrder"}], "process.hook:afterCreateOrder": [{"moduleId": "@wsc-tee-trade/xhs-order-core~igNxLRdK", "name": "hook:after<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "data.shopInfo": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "shop"}}}, {"id": "@wsc-tee-trade/trade-buy-page-setup~lyUjPNSI", "extensionName": "@wsc-tee-trade/trade-buy-page-setup", "extensionVersion": "4.4.8", "isRemote": false, "bindings": {}}, {"id": "@wsc-tee-trade/trade-buy-drug~vmOvqvMl", "extensionName": "@wsc-tee-trade/trade-buy-drug", "extensionVersion": "0.2.6", "isRemote": false, "bindings": {"data.drugState": null, "event.onDrugPageShow": null}}, {"id": "@wsc-tee-trade/trade-buy-goods~WlUoDiBm", "extensionName": "@wsc-tee-trade/trade-buy-goods", "extensionVersion": "2.0.8", "isRemote": false, "bindings": {"data.appShop": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "shop"}, "process.fetchShow": [{"moduleId": "@wsc-tee-trade/trade-buy-core~CNblvZOR", "name": "confirmOrder"}], "widget.PostageCell": [{"moduleId": "trade-buy-postage-cell~QnCXFPyO", "name": "Main"}], "widget.Presale": [{"moduleId": "@wsc-tee-trade/trade-buy-goods~WlUoDiBm", "name": "Presale"}], "widget.ExtraFees": [{"moduleId": "@wsc-tee-trade/trade-buy-goods~WlUoDiBm", "name": "ExtraFees"}]}}, {"id": "@ext-tee-wsc-decorate/theme-color~JqvNVwQZ", "extensionName": "@ext-tee-wsc-decorate/theme-color", "extensionVersion": "1.4.4", "isRemote": false}, {"id": "@assets-tee-extensions/guarantee-freight-bar~hHytnLjk", "extensionName": "@assets-tee-extensions/guarantee-freight-bar", "extensionVersion": "2.0.0", "isRemote": false, "bindings": {"data.paddingConfig": {"moduleId": "@wsc-tee-trade/trade-buy-core~CNblvZOR", "name": "guaranteeFreightBarPaddingConfig"}, "data.defaultFreightInsurance": null, "data.defaultYzGuarantee": null}}, {"id": "@assets-tee-extensions/guarantee-components~ReLtkTin", "extensionName": "@assets-tee-extensions/guarantee-components", "extensionVersion": "1.2.13", "isRemote": false}, {"id": "@assets-tee-extensions/guarantee-ensure~dLcrLIXO", "properties": {"yzGuaranteeEnsureMt": 32}, "extensionName": "@assets-tee-extensions/guarantee-ensure", "extensionVersion": "1.0.13", "isRemote": false}, {"id": "@wsc-tee-trade/trade-buy-submit~iyvPfGjL", "extensionName": "@wsc-tee-trade/trade-buy-submit", "extensionVersion": "5.0.10", "isRemote": false, "bindings": {"widget.UserAuthorize": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Main"}], "event.cashier:pay:fail": [{"moduleId": "@assets-tee-extensions/cashier-pre~iKgqOPSW", "name": "cashier:pay:fail"}], "event.cashier:pay:success": [{"moduleId": "@assets-tee-extensions/cashier-pre~iKgqOPSW", "name": "cashier:pay:success"}], "data.showSubscribeGuide": null, "process.invoke-protocol": null, "process.queryPayChannels": [{"moduleId": "@wsc-tee-trade/trade-buy-pay-view~EnqbLHqh", "name": "queryPayChannels"}], "process.doPay": [{"moduleId": "@wsc-tee-trade/trade-buy-pay-view~EnqbLHqh", "name": "doPay"}], "process.extPay": [{"moduleId": "@assets-tee-extensions/cashier~RsgazGAT", "name": "extPay"}], "process.startPay": [{"moduleId": "@wsc-tee-trade/trade-buy-pay-view~EnqbLHqh", "name": "startPay"}], "process.beforeStartPay": [{"moduleId": "@wsc-tee-trade/xhs-order-core~igNxLRdK", "name": "beforeStartPay"}]}}, {"id": "@ext-tee-wsc-ump/trade-buy-ump-data~pZmVaTcf", "extensionName": "@ext-tee-wsc-ump/trade-buy-ump-data", "extensionVersion": "0.5.5", "isRemote": false, "bindings": {"process.fetchShow": [{"moduleId": "@wsc-tee-trade/trade-buy-core~CNblvZOR", "name": "confirmOrder"}]}}, {"id": "@wsc-tee-trade/trade-buy-price~ebCNDGHK", "extensionName": "@wsc-tee-trade/trade-buy-price", "extensionVersion": "2.1.7", "isRemote": false, "bindings": {"widget.Presale": [{"moduleId": "@wsc-tee-trade/trade-buy-price~ebCNDGHK", "name": "Presale"}]}}, {"id": "@wsc-tee-trade/trade-buy-prior-use~QaLWscbf", "extensionName": "@wsc-tee-trade/trade-buy-prior-use", "extensionVersion": "1.2.1", "isRemote": false, "bindings": {"data.themeColors": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "theme"}}}, {"id": "@wsc-tee-trade/trade-buy-pay-view~EnqbLHqh", "properties": {}, "extensionName": "@wsc-tee-trade/trade-buy-pay-view", "extensionVersion": "3.0.29", "isRemote": false, "bindings": {"process.startPay_preCashier": [{"moduleId": "@assets-tee-extensions/cashier-pre~iKgqOPSW", "name": "pay"}], "data.shop": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "shop"}, "event.cashier:close": [{"moduleId": "@assets-tee-extensions/cashier~RsgazGAT", "name": "cashier:close"}], "event.cashier:pay:success": [{"moduleId": "@assets-tee-extensions/cashier~RsgazGAT", "name": "cashier:pay:success"}], "process.doPay": [{"moduleId": "@assets-tee-extensions/cashier~RsgazGAT", "name": "doPay"}], "process.queryPayChannels": [{"moduleId": "@assets-tee-extensions/cashier~RsgazGAT", "name": "queryPayChannels"}], "process.startPay": [{"moduleId": "@assets-tee-extensions/cashier~RsgazGAT", "name": "startPay"}], "data.orderNo": null, "event.cashier:pay:fail": null}}, {"id": "@wsc-tee-trade/trade-buy-compress-upload~twTAhPWK", "extensionName": "@wsc-tee-trade/trade-buy-compress-upload", "extensionVersion": "1.0.3", "isRemote": false}, {"id": "@wsc-tee-trade/cart-present-popup~cvTYLOAY", "extensionName": "@wsc-tee-trade/cart-present-popup", "extensionVersion": "2.0.2", "isRemote": false}, {"id": "@ext-tee-cps/growth-params~RAcPFiXi", "extensionName": "@ext-tee-cps/growth-params", "extensionVersion": "1.1.0", "isRemote": false}, {"id": "@wsc-tee-trade/trade-buy-block-container~wKmgVdYi", "extensionName": "@wsc-tee-trade/trade-buy-block-container", "extensionVersion": "1.0.6", "isRemote": false, "bindings": {"widget.MainContent": [{"moduleId": "@wsc-tee-trade/trade-buy-pay-view~EnqbLHqh", "name": "Installment"}]}}, {"id": "@ext-tee-guide/behalf-order-trade-buy-block~hxjmcfJK", "extensionName": "@ext-tee-guide/behalf-order-trade-buy-block", "extensionVersion": "1.0.0", "isRemote": false}, {"id": "@assets-tee-extensions/cashier~RsgazGAT", "extensionName": "@assets-tee-extensions/cashier", "extensionVersion": "2.6.14-beta.0+hotfix-pay-not-redirect-20221027", "isRemote": false, "bindings": {"process.beforePay": [{"moduleId": "@wsc-tee-trade/trade-buy-pay-view~EnqbLHqh", "name": "onPayItemClick"}, {"moduleId": "@wsc-tee-trade/trade-buy-pay-view~EnqbLHqh", "name": "handleBeforePay"}], "process.onPayItemClick": [{"moduleId": "@wsc-tee-trade/trade-buy-pay-view~EnqbLHqh", "name": "onPayItemClick"}], "widget.UserAuthorize": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Main"}], "data.shop": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "shop"}, "process.onPaySuccessSync": null}}, {"id": "@assets-tee-extensions/cashier~dWXpmGYo", "extensionName": "@assets-tee-extensions/cashier", "extensionVersion": "2.6.14-beta.0+hotfix-pay-not-redirect-20221027", "isRemote": false, "bindings": {"process.onPayItemClick": [{"moduleId": "@wsc-tee-trade/trade-buy-pay-view~EnqbLHqh", "name": "onPayItemClick"}], "widget.UserAuthorize": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Main"}], "data.biz": {"moduleId": "@retail-tee-prepaid/prepaid~SeJOhfBK", "name": "biz"}, "data.quickMode": {"moduleId": "@retail-tee-prepaid/prepaid~SeJOhfBK", "name": "quickMode"}, "data.shop": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "shop"}, "process.onPaySuccessSync": null, "process.beforePay": [{"moduleId": "@wsc-tee-trade/trade-buy-pay-view~EnqbLHqh", "name": "handleBeforePay"}]}}, {"id": "@wsc-tee-trade/retail-order~wordavPC", "extensionName": "@wsc-tee-trade/retail-order", "extensionVersion": "0.4.2", "isRemote": false, "bindings": {"process.fetchShow": [{"moduleId": "@wsc-tee-trade/trade-buy-core~CNblvZOR", "name": "confirmOrder"}], "data.shop": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "shop"}, "data.dine": null, "data.isRetailWeappScene": {"moduleId": "@wsc-tee-trade/retail-order~wordavPC", "name": "isRetailWeappScene"}, "data.kdtId": null, "data.pickUpWay": {"moduleId": "@wsc-tee-trade/trade-buy-core~CNblvZOR", "name": "pickUpWay"}, "data.retailPickUpWays": {"moduleId": "@wsc-tee-trade/retail-order~wordavPC", "name": "retailPickUpWays"}, "data.retailSelfFetchContact": {"moduleId": "@wsc-tee-trade/retail-order~wordavPC", "name": "retailSelfFetchContact"}}}, {"id": "trade-buy-postage-cell~QnCXFPyO", "extensionName": "trade-buy-postage-cell", "extensionVersion": "0.0.1", "isRemote": false}, {"id": "@ext-tee-shop/shop-cert-notice~NzIIzmlO", "extensionName": "@ext-tee-shop/shop-cert-notice", "extensionVersion": "1.1.3", "isRemote": false}, {"id": "@assets-tee-extensions/cashier-pre~iKgqOPSW", "extensionName": "@assets-tee-extensions/cashier-pre", "extensionVersion": "2.3.8", "isRemote": false, "bindings": {"data.amount": {"moduleId": "@wsc-tee-trade/trade-buy-pay-view~EnqbLHqh", "name": "orderAmount"}, "data.mobile": {"moduleId": "@wsc-tee-trade/trade-buy-pay-view~EnqbLHqh", "name": "buyerPhone"}, "data.traceId": {"moduleId": "@wsc-tee-trade/trade-buy-core~CNblvZOR", "name": "book<PERSON>ey"}, "data.excludePayChannels": {"moduleId": "@wsc-tee-trade/trade-buy-pay-view~EnqbLHqh", "name": "excludePayTools"}, "process.onPayItemClick": [{"moduleId": "@wsc-tee-trade/trade-buy-pay-view~EnqbLHqh", "name": "onPayItemClick"}]}}, {"id": "@wsc-tee-trade/trade-buy-service-block~KOODDfWl", "extensionName": "@wsc-tee-trade/trade-buy-service-block", "extensionVersion": "1.0.5", "isRemote": false}, {"id": "@wsc-tee-trade/trade-buy-misc-pre~rLXfqoRW", "extensionName": "@wsc-tee-trade/trade-buy-misc-pre", "extensionVersion": "0.1.5", "isRemote": false, "bindings": {"data.shop": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "shop"}, "data.display": null, "data.goods": null, "data.orderFinalPrice": null, "data.orderKeepApply": null, "data.orderPaid": null, "data.pointDeduction": null, "data.shopConfig": null, "lambda.hexToRgb": null, "process.navigateToTradeBuy": null, "widget.OrderKeepDialog": null}}, {"id": "@wsc-tee-trade/trade-buy-ump-block~jOQaHusv", "extensionName": "@wsc-tee-trade/trade-buy-ump-block", "extensionVersion": "1.0.7", "isRemote": false, "bindings": {"widget.UserAuthorize": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Main"}], "widget.ExtraFees": [{"moduleId": "@wsc-tee-trade/trade-buy-ump-block~jOQaHusv", "name": "ExtraFees"}], "event.submit:act": [{"moduleId": "@ext-tee-wsc-goods/base-common-sku~zCdvMfGd", "name": "sku:submit"}], "process.launchFastJoinSDK": [{"moduleId": "@ext-tee-user/fast-join-sdk~XvGJdnqz", "name": "launchFastJoinSDK"}]}}, {"id": "@wsc-tee-trade/trade-buy-address-pre~KwliftbD", "extensionName": "@wsc-tee-trade/trade-buy-address-pre", "extensionVersion": "1.0.13", "isRemote": false, "bindings": {"data.appShop": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "shop"}, "data.retailHouseNumberRequired": {"moduleId": "@wsc-tee-trade/retail-order~wordavPC", "name": "houseNumberRequired"}, "data.themeVars": {"moduleId": "@ext-tee-wsc-decorate/theme-color~JqvNVwQZ", "name": "themeCSS"}, "process.fetchShow": [{"moduleId": "@wsc-tee-trade/trade-buy-core~CNblvZOR", "name": "confirmOrder"}], "process.hook:beforeGetDefaultSelfFetch": [{"moduleId": "@wsc-tee-trade/retail-order~wordavPC", "name": "callHookBeforeGetDefaultSelfFetch"}], "data.shop": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "shop"}, "data.isRetailOrderScene": null, "data.isRetailWeappScene": null, "data.isScanCodeBuy": null, "data.retailSelfFetchContact": {"moduleId": "@wsc-tee-trade/retail-order~wordavPC", "name": "retailSelfFetchContact"}, "data.tradeAddressVisible": null, "widget.IdcardMain": [{"moduleId": "@wsc-tee-trade/trade-buy-id-card~xKuDWjlx", "name": "Main"}], "process.selectRetailContact": [{"moduleId": "@wsc-tee-trade/retail-order~wordavPC", "name": "selectRetailContact"}], "widget.GetPhoneButton": null}}, {"id": "@wsc-tee-trade/trade-buy-misc~qbcwTCHI", "extensionName": "@wsc-tee-trade/trade-buy-misc", "extensionVersion": "2.1.1", "isRemote": false, "bindings": {"data.orderGoods": {"moduleId": "@wsc-tee-trade/trade-buy-core~CNblvZOR", "name": "goods"}, "data.orderNo": null}}, {"id": "@wsc-tee-trade/trade-buy-header-container~sXEkDyzO", "extensionName": "@wsc-tee-trade/trade-buy-header-container", "extensionVersion": "0.0.1", "isRemote": false}, {"id": "@wsc-tee-trade/trade-buy-block-container~jjshYoHO", "extensionName": "@wsc-tee-trade/trade-buy-block-container", "extensionVersion": "1.0.6", "isRemote": false, "bindings": {"widget.MainContent": [{"moduleId": "@wsc-tee-trade/trade-buy-misc~qbcwTCHI", "name": "HaitaoFooterNotice"}]}}, {"id": "@retail-tee-prepaid/prepaid~SeJOhfBK", "extensionName": "@retail-tee-prepaid/prepaid", "extensionVersion": "0.0.4", "isRemote": false, "bindings": {"event.cashier:pay:prepaid:success": [{"moduleId": "@assets-tee-extensions/cashier~dWXpmGYo", "name": "cashier:pay:success"}], "event.cashier:pay:prepaid:fail": [{"moduleId": "@assets-tee-extensions/cashier~dWXpmGYo", "name": "cashier:pay:fail"}], "event.cashier:pay:prepaid:close": [{"moduleId": "@assets-tee-extensions/cashier~dWXpmGYo", "name": "cashier:close"}], "process.prepaidStartPay": [{"moduleId": "@assets-tee-extensions/cashier~dWXpmGYo", "name": "startPay"}], "process.fetchShow": [{"moduleId": "@wsc-tee-trade/trade-buy-core~CNblvZOR", "name": "confirmOrder"}]}}, {"id": "@wsc-tee-trade/trade-buy-popup-container~aJKfcGYu", "extensionName": "@wsc-tee-trade/trade-buy-popup-container", "extensionVersion": "0.0.1", "isRemote": false, "bindings": {"widget.PopupContent": [{"moduleId": "@wsc-tee-trade/trade-buy-ump-block~jOQaHusv", "name": "Postagetool"}, {"moduleId": "@wsc-tee-trade/trade-buy-ump-block~jOQaHusv", "name": "CouponList"}, {"moduleId": "@wsc-tee-trade/trade-buy-ump-block~jOQaHusv", "name": "MembershipDialog"}, {"moduleId": "@wsc-tee-trade/trade-buy-ump-block~jOQaHusv", "name": "PrepayCardPopup"}, {"moduleId": "@wsc-tee-trade/trade-buy-ump-block~jOQaHusv", "name": "ActivityDialog"}, {"moduleId": "@wsc-tee-trade/trade-buy-common-popup~YYDHfqZK", "name": "InvoiceActionSheet"}, {"moduleId": "@wsc-tee-trade/trade-buy-ump-block~jOQaHusv", "name": "PointDeductionPopup"}, {"moduleId": "@retail-tee-prepaid/prepaid~SeJOhfBK", "name": "RechargeGiftPack"}, {"moduleId": "@ext-tee-live/draggable-live-player~qfKXaDTZ", "name": "DraggableLivePlayer"}]}}, {"id": "@wsc-tee-trade/trade-buy-cashier-pre~RcWwXrKB", "properties": {"block": true}, "extensionName": "@wsc-tee-trade/trade-buy-cashier-pre", "extensionVersion": "0.0.3", "isRemote": false, "bindings": {"widget.Cashier": [{"moduleId": "@assets-tee-extensions/cashier-pre~iKgqOPSW", "name": "CellGroup"}]}}, {"id": "@wsc-tee-trade/retail-order-error-info~hzjErGEf", "extensionName": "@wsc-tee-trade/retail-order-error-info", "extensionVersion": "0.0.2-beta.0+hotfix-order-error-enter-shop", "isRemote": false, "properties": {"source": "trade-buy"}, "bindings": {"data.shop": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "shop"}}}, {"id": "@wsc-tee-trade/trade-buy-reward-popup~bjtTCsFN", "extensionName": "@wsc-tee-trade/trade-buy-reward-popup", "extensionVersion": "0.0.0", "isRemote": false}, {"id": "trade-buy-privacy-bill~UkvefXpO", "extensionName": "trade-buy-privacy-bill", "extensionVersion": "0.0.1", "isRemote": false, "properties": {"isOrderPage": true}}, {"id": "@ext-tee-wsc-decorate/page-style~gGpmuMgP", "extensionName": "@ext-tee-wsc-decorate/page-style", "extensionVersion": "0.0.1", "isRemote": false, "properties": {"useAppStyleIcon": true}}, {"id": "@wsc-tee-shop/footer~BXYpISCd", "extensionName": "@wsc-tee-shop/footer", "extensionVersion": "1.5.6", "isRemote": false, "properties": {"isShowNav": false}}, {"id": "@wsc-tee-trade/trade-buy-ump-wrapper~ZXtnVvry", "extensionName": "@wsc-tee-trade/trade-buy-ump-wrapper", "extensionVersion": "0.0.0", "isRemote": false}, {"id": "@wsc-tee-trade/trade-buy-service-wrapper~hFzqwxxK", "extensionName": "@wsc-tee-trade/trade-buy-service-wrapper", "extensionVersion": "0.0.0", "isRemote": false}, {"id": "@wsc-tee-trade/trade-buy-common-popup~YYDHfqZK", "extensionName": "@wsc-tee-trade/trade-buy-common-popup", "extensionVersion": "0.0.0", "isRemote": false}, {"id": "@ext-tee-wsc-goods/base-common-sku~zCdvMfGd", "extensionName": "@ext-tee-wsc-goods/base-common-sku", "extensionVersion": "0.0.0", "isRemote": false, "bindings": {"event.afterSkuSubmit": [{"moduleId": "@wsc-tee-trade/trade-buy-ump-block~jOQaHusv", "name": "sku:afterSubmit"}, {"moduleId": "@wsc-tee-trade/cart-present-popup~cvTYLOAY", "name": "present-sku:after<PERSON><PERSON><PERSON>"}]}}, {"id": "@wsc-tee-trade/trade-buy-id-card~xKuDWjlx", "extensionName": "@wsc-tee-trade/trade-buy-id-card", "extensionVersion": "0.0.0", "isRemote": false, "bindings": {"widget.tradeBuyIdCardPopup": [{"moduleId": "@wsc-tee-trade/trade-buy-id-card-popup~ZNFlmPZw", "name": "<PERSON>dcard<PERSON><PERSON><PERSON>"}]}}, {"id": "@wsc-tee-trade/retail-exclusive~nclKCsoT", "extensionName": "@wsc-tee-trade/retail-exclusive", "extensionVersion": "0.0.0", "isRemote": false}, {"id": "@wsc-tee-trade/xhs-order-core~igNxLRdK", "extensionName": "@wsc-tee-trade/xhs-order-core", "extensionVersion": "0.0.1", "isRemote": false}, {"id": "@wsc-tee-trade/trade-buy-common-cloud~STHhjDRW", "extensionName": "@wsc-tee-trade/trade-buy-common-cloud", "extensionVersion": "0.0.0", "isRemote": false}, {"id": "@ext-tee-user/fast-join-sdk~XvGJdnqz", "extensionName": "@ext-tee-user/fast-join-sdk", "extensionVersion": "0.0.0", "isRemote": false}, {"id": "@ext-tee-live/draggable-live-player~qfKXaDTZ", "extensionName": "@ext-tee-live/draggable-live-player", "extensionVersion": "0.0.0", "isRemote": false, "properties": {"removeLiveUrlCookie": true}}]}