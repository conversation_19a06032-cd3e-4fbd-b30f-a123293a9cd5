{"id": "732ce44f-b80b-423f-a6bd-e69e6cace882", "config": {"navigationBarTitleText": "购物车", "enablePullDownRefresh": true, "navigationStyle": "custom"}, "routes": ["/wsctrade/cart"], "containers": [{"contentType": "container", "layout": "column", "contents": [{"contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/cart-shop-header~mqHVkqaX", "@wsc-tee-trade/cart-coupon-addon-bar~Jojrwozq", "@wsc-tee-trade/cart-coupon-bar~efGaYvHr", "@ext-tee-wsc-decorate/elderly-oriented~E7586E9C"]}]}, {"contentType": "container", "layout": "column", "contents": [{"blockName": "login-tips", "contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/cart-block-container~YWRUAYYh"]}, {"blockName": "valid-goods", "contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/cart-valid-goods~uIZJGWXz"]}, {"blockName": "invalid-goods", "contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/cart-invalid-goods~exfXGfbv"]}, {"contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/cart-empty-tip~FcAfxdcU"]}, {"blockName": "cps-goods-recommend", "contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/cart-block-container~RGJHGDGT"]}, {"blockName": "recommend-block", "contentType": "module", "layout": "column", "contents": ["@wsc-tee-statcenter/recommend-goods~DccgYcfd"]}, {"blockName": "cart-bottom", "contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/cart-submit~OckeIcaK"]}, {"blockName": "fast-join-block", "contentType": "module", "layout": "column", "contents": ["@ext-tee-user/fast-join-sdk~bDMtnuoo"]}]}, {"contentType": "container", "layout": "column", "contents": [{"contentType": "module", "layout": "column", "contents": ["@wsc-tee-trade/cart-present-popup~eDARjzip", "@wsc-tee-trade/trade-buy-reward-popup~gRnZvFWL", "@wsc-tee-trade/common-widgets~iShNcOTt", "@wsc-tee-shop/footer~pjXHnoOX", "@ext-tee-wsc-decorate/page-style~wtipUiVX", "@wsc-tee-trade/cart-order-keep~EIahQSeD", "@ext-tee-wsc-goods/base-common-sku~pqqDUSwC", "@ext-tee-live/draggable-live-player~hhwqghxC"]}], "style": {"padding-bottom": "50px"}}], "modules": [{"id": "@wsc-tee-statcenter/recommend-goods~DccgYcfd", "properties": {}, "extensionName": "@wsc-tee-statcenter/recommend-goods", "extensionVersion": "1.6.16", "isRemote": false, "bindings": {"data.enableShow": {"moduleId": "@wsc-tee-trade/cart-valid-goods~uIZJGWXz", "name": "isValidGoodsLoadFinish"}}}, {"id": "@ext-tee-wsc-decorate/theme-color~OfQVkTbf", "extensionName": "@ext-tee-wsc-decorate/theme-color", "extensionVersion": "1.4.4", "isRemote": false, "bindings": {"data.kdtId": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "kdtId"}}}, {"id": "@wsc-tee-trade/cart-ump~vXUKhNqs", "extensionName": "@wsc-tee-trade/cart-ump", "extensionVersion": "1.3.1", "isRemote": false, "bindings": {"event.exchangeGoodsSku:submit": [{"moduleId": "@ext-tee-wsc-goods/base-common-sku~pqqDUSwC", "name": "sku:submit"}], "event.exchangeGoodsSku:hide": [{"moduleId": "@ext-tee-wsc-goods/base-common-sku~pqqDUSwC", "name": "sku:hide"}]}}, {"id": "@wsc-tee-trade/cart-page-setup~ostGtmui", "properties": {}, "asyncInit": true, "extensionName": "@wsc-tee-trade/cart-page-setup", "extensionVersion": "1.7.1", "isRemote": false}, {"id": "@wsc-tee-trade/cart-shop-header~mqHVkqaX", "extensionName": "@wsc-tee-trade/cart-shop-header", "extensionVersion": "1.3.1", "isRemote": false}, {"id": "@wsc-tee-trade/cart-empty-tip~FcAfxdcU", "extensionName": "@wsc-tee-trade/cart-empty-tip", "extensionVersion": "1.0.32", "isRemote": false}, {"id": "@wsc-tee-trade/cart-shopping-circle~iJrsEThN", "extensionName": "@wsc-tee-trade/cart-shopping-circle", "extensionVersion": "1.0.25", "isRemote": false}, {"id": "@wsc-tee-trade/cart-valid-goods~uIZJGWXz", "extensionName": "@wsc-tee-trade/cart-valid-goods", "extensionVersion": "2.0.4", "isRemote": false, "bindings": {"widget.PresentGoods": [{"moduleId": "@wsc-tee-trade/common-widgets~iShNcOTt", "name": "PresentGoods"}], "widget.Goods": [{"moduleId": "@wsc-tee-trade/cart-valid-goods~uIZJGWXz", "name": "Goods"}], "event.cartGoodsSku:submit": [{"moduleId": "@ext-tee-wsc-goods/base-common-sku~pqqDUSwC", "name": "sku:submit"}], "event.cartPresentSku:submit": [{"moduleId": "@ext-tee-wsc-goods/base-common-sku~pqqDUSwC", "name": "sku:custom"}], "event.cartPresentSku:selected": [{"moduleId": "@ext-tee-wsc-goods/base-common-sku~pqqDUSwC", "name": "sku:selected"}], "event.cartGoodsSku:hide": [{"moduleId": "@ext-tee-wsc-goods/base-common-sku~pqqDUSwC", "name": "sku:hide"}]}}, {"id": "@wsc-tee-trade/cart-invalid-goods~exfXGfbv", "properties": {}, "extensionName": "@wsc-tee-trade/cart-invalid-goods", "extensionVersion": "1.6.4", "isRemote": false, "bindings": {"widget.InvalidMask": [{"moduleId": "@wsc-tee-trade/cart-invalid-goods~exfXGfbv", "name": "InvalidMask"}], "widget.Goods": [{"moduleId": "@wsc-tee-trade/cart-invalid-goods~exfXGfbv", "name": "Goods"}]}}, {"id": "@wsc-tee-trade/cart-submit~OckeIcaK", "extensionName": "@wsc-tee-trade/cart-submit", "extensionVersion": "2.0.1", "isRemote": false, "bindings": {"process.beforeBuyWithGoods": [{"moduleId": "@wsc-tee-trade/xhs-order-core~knUdPYcE", "name": "beforeBuyWithGoods"}]}}, {"id": "@wsc-tee-trade/cart-order-keep~EIahQSeD", "properties": {}, "extensionName": "@wsc-tee-trade/cart-order-keep", "extensionVersion": "1.1.18", "isRemote": false, "bindings": {"event.open": [{"moduleId": "@wsc-tee-trade/cart-page-setup~ostGtmui", "name": "ORDER_KEEP:open"}], "data.kdtId": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "kdtId"}}}, {"id": "@wsc-tee-trade/cart-coupon-bar~efGaYvHr", "extensionName": "@wsc-tee-trade/cart-coupon-bar", "extensionVersion": "2.0.0", "isRemote": false, "bindings": {"widget.UserAuthorize": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Main"}], "process.launchFastJoinSDK": [{"moduleId": "@ext-tee-user/fast-join-sdk~bDMtnuoo", "name": "launchFastJoinSDK"}]}}, {"id": "@wsc-tee-trade/cart-present-popup~eDARjzip", "extensionName": "@wsc-tee-trade/cart-present-popup", "extensionVersion": "2.0.2", "isRemote": false, "bindings": {"event.present-sku:fetch": [{"moduleId": "@wsc-tee-trade/cart-valid-goods~uIZJGWXz", "name": "cartPresentSku:fetch"}]}}, {"id": "@wsc-tee-trade/common-widgets~iShNcOTt", "extensionName": "@wsc-tee-trade/common-widgets", "extensionVersion": "2.1.1", "isRemote": false, "bindings": {"widget.InvalidMask": [{"moduleId": "@wsc-tee-trade/common-widgets~iShNcOTt", "name": "InvalidMask"}], "widget.Price": [{"moduleId": "@wsc-tee-trade/common-widgets~iShNcOTt", "name": "Price"}]}}, {"id": "@wsc-tee-trade/cart-coupon-addon-bar~Jojrwozq", "extensionName": "@wsc-tee-trade/cart-coupon-addon-bar", "extensionVersion": "1.0.0", "isRemote": false, "bindings": {"widget.UserAuthorize": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Main"}], "process.launchFastJoinSDK": [{"moduleId": "@ext-tee-user/fast-join-sdk~bDMtnuoo", "name": "launchFastJoinSDK"}]}}, {"id": "@ext-tee-wsc-goods/combine-goods-block~nhObVhHs", "extensionName": "@ext-tee-wsc-goods/combine-goods-block", "extensionVersion": "0.0.13", "isRemote": false}, {"id": "@wsc-tee-shop/footer~pjXHnoOX", "extensionName": "@wsc-tee-shop/footer", "extensionVersion": "1.5.6", "isRemote": false}, {"id": "@wsc-tee-trade/cart-block-container~YWRUAYYh", "extensionName": "@wsc-tee-trade/cart-block-container", "extensionVersion": "0.0.0", "isRemote": false}, {"id": "@wsc-tee-trade/cart-block-container~RGJHGDGT", "extensionName": "@wsc-tee-trade/cart-block-container", "extensionVersion": "0.0.0", "isRemote": false}, {"id": "@ext-tee-wsc-decorate/page-style~wtipUiVX", "extensionName": "@ext-tee-wsc-decorate/page-style", "extensionVersion": "0.0.1", "isRemote": false, "properties": {"useAppStyleIcon": true}}, {"id": "@wsc-tee-trade/trade-buy-reward-popup~gRnZvFWL", "extensionName": "@wsc-tee-trade/trade-buy-reward-popup", "extensionVersion": "0.0.0", "isRemote": false, "bindings": {"event.reward:show": [{"moduleId": "@wsc-tee-trade/cart-submit~OckeIcaK", "name": "reward:show"}], "widget.PresentGoods": [{"moduleId": "@wsc-tee-trade/common-widgets~iShNcOTt", "name": "PresentGoods"}], "widget.CouponItem": [{"moduleId": "@wsc-tee-trade/common-widgets~iShNcOTt", "name": "CouponItem"}]}}, {"id": "@ext-tee-wsc-goods/base-common-sku~pqqDUSwC", "extensionName": "@ext-tee-wsc-goods/base-common-sku", "extensionVersion": "0.0.0", "isRemote": false, "bindings": {"event.afterSkuSubmit": [{"moduleId": "@wsc-tee-trade/cart-page-setup~ostGtmui", "name": "cartGoodsSku:hide"}, {"moduleId": "@wsc-tee-trade/cart-valid-goods~uIZJGWXz", "name": "cartGoodsSku:afterSubmit"}, {"moduleId": "@wsc-tee-trade/cart-valid-goods~uIZJGWXz", "name": "cartPresentSku:afterSubmit"}, {"moduleId": "@wsc-tee-trade/cart-present-popup~eDARjzip", "name": "present-sku:after<PERSON><PERSON><PERSON>"}, {"moduleId": "@wsc-tee-trade/cart-ump~vXUKhNqs", "name": "exchangeGoodsSku:afterSubmit"}]}}, {"id": "@ext-tee-user/fast-join-sdk~bDMtnuoo", "extensionName": "@ext-tee-user/fast-join-sdk", "extensionVersion": "0.0.0", "isRemote": false}, {"id": "@wsc-tee-trade/xhs-order-core~knUdPYcE", "extensionName": "@wsc-tee-trade/xhs-order-core", "extensionVersion": "0.0.1", "isRemote": false}, {"id": "@ext-tee-live/draggable-live-player~hhwqghxC", "extensionName": "@ext-tee-live/draggable-live-player", "extensionVersion": "0.0.0", "isRemote": false, "properties": {"removeLiveUrlCookie": true}}]}