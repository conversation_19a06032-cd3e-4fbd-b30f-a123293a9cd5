{"name": "wsc-tee-h5", "plugins": {"dmc": {"biz": "yzWeb"}, "logger": {"yai": "wsc_c", "batch": true, "autoSpm": true, "autoEnterpage": true, "requestUrl": "https://tj1.youzan.com/v3/js/log"}, "hummer": {"appKey": "wsc-tee-h5", "error": {"onNavigate": false}}}, "modules": [{"id": "@passport-tee/api~HsChYbYC", "originalId": "70745021-5fff-474a-b57b-afa95eb32219", "provide": {"lambda": [{"name": "login", "alias": "login"}, {"name": "forceLogin", "alias": "forceLogin"}, {"name": "getUserInfo", "alias": "getUserInfo"}, {"name": "getAppId", "alias": "getAppId"}, {"name": "getAuthType", "alias": "getAuthType"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "alias": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "getPlatform", "alias": "getPlatform"}, {"name": "getHostApp", "alias": "getHostApp"}], "data": [{"name": "appId", "alias": "appId"}, {"name": "miniprogram", "alias": "miniprogram"}, {"name": "platformInfo", "alias": "platformInfo"}], "event": [{"name": "login", "alias": "login"}]}, "consume": {}, "extensionName": "@passport-tee/api", "extensionVersion": "1.7.2", "isRemote": false}, {"id": "@wsc-tee-shop/multi-store~brNzIGqQ", "originalId": "411a9bc4-71e1-42dc-aa63-a43bb7863128", "provide": {"lambda": [{"name": "isMultiStoreAsync", "alias": "isMultiStoreAsync"}], "data": [{"name": "offlineId", "alias": "offlineId"}, {"name": "isMultiStore", "alias": "isMultiStore"}, {"name": "offlineData", "alias": "offlineData"}, {"name": "openHideStore", "alias": "openHideStore"}], "process": [{"name": "setOfflineId", "alias": "setOfflineId"}, {"name": "getOfflineData", "alias": "getOfflineData"}, {"name": "switchMultiStore", "alias": "switchMultiStore"}]}, "consume": {"lambda": [{"origin": "APP", "originalName": "login", "name": "login"}]}, "extensionName": "@wsc-tee-shop/multi-store", "extensionVersion": "1.1.3", "isRemote": false}, {"id": "@passport-tee/user-authorize~FHzemhCn", "extensionName": "@passport-tee/user-authorize", "extensionVersion": "2.0.0", "isRemote": false, "bindings": {"data.kdtId": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "kdtId"}, "process.mobileChange": [{"moduleId": "@wsc-tee-shop/chain-store~dIZGeJmH", "name": "enterShopAfterLogin"}], "widget.UserAuthorize": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Main"}], "widget.ProtocolContent": [{"moduleId": "@passport-tee/protocol~Duv5tmrK", "name": "ProtocolContent"}]}}, {"id": "@passport-tee/protocol~Duv5tmrK", "extensionName": "@passport-tee/protocol", "extensionVersion": "1.0.0", "isRemote": false}, {"id": "@wsc-tee-shop/shop-core~awkeKIEV", "provide": {"data": [{"name": "kdtId", "alias": "kdtId"}, {"name": "rootKdtId", "alias": "rootKdtId"}, {"name": "theme", "alias": "theme"}, {"name": "shop", "alias": "shop"}], "process": [{"name": "setKdtId", "alias": "setKdtId"}, {"name": "readConf", "alias": "readConf"}]}, "consume": {}, "extensionName": "@wsc-tee-shop/shop-core", "extensionVersion": "1.1.8", "isRemote": false}, {"id": "@wsc-tee-shop/chain-store~dIZGeJmH", "provide": {"process": [{"name": "autoEnterShop", "alias": "autoEnterShop"}, {"name": "enterShopSelect", "alias": "enterShopSelect"}, {"name": "enterShopAfterLogin", "alias": "enterShopAfterLogin"}, {"name": "setApplicationScene", "alias": "setApplicationScene"}, {"name": "getOrderEnterShopPolicy", "alias": "getOrderEnterShopPolicy"}, {"name": "handleUrlWithShopAutoEnter", "alias": "handleUrlWithShopAutoEnter"}]}, "consume": {}, "extensionName": "@wsc-tee-shop/chain-store", "extensionVersion": "1.0.5", "isRemote": false}, {"id": "@logger-ranta/h5-setter~QyPbYcKf", "provide": {}, "consume": {}, "extensionName": "@logger-ranta/h5-setter", "extensionVersion": "1.0.11", "isRemote": false}, {"id": "@wsc-tee-trade/trade-buy-page-comm~nZgCBpro", "provide": {"data": [{"name": "commTest", "alias": "commTest"}, {"name": "kdtId", "alias": "kdtId_0"}, {"name": "locateCityInfo", "alias": "locateCityInfo"}, {"name": "isInDrugWhiteList", "alias": "isInDrugWhiteList"}], "event": [{"name": "$message", "alias": "$message"}], "process": [{"name": "locateCity", "alias": "locateCity"}, {"name": "locateLngLat", "alias": "locateLngLat"}, {"name": "getLocation", "alias": "getLocation"}], "lambda": [{"name": "setDb", "alias": "setDb"}, {"name": "getDb", "alias": "getDb"}, {"name": "deleteDb", "alias": "deleteDb"}, {"name": "triggerEvent", "alias": "triggerEvent"}, {"name": "onEventOnce", "alias": "onEventOnce"}, {"name": "onEvent", "alias": "onEvent"}, {"name": "tryLocation", "alias": "tryLocation"}, {"name": "amap", "alias": "amap"}, {"name": "locateCityByPosition", "alias": "locateCityByPosition"}, {"name": "locateCityByIp", "alias": "locateCityByIp"}]}, "consume": {}, "extensionName": "@wsc-tee-trade/trade-buy-page-comm", "extensionVersion": "2.1.6", "isRemote": false}, {"id": "@ext-tee-navigate/web-navigator-init~UfrgJeGt", "provide": {}, "consume": {}, "extensionName": "@ext-tee-navigate/web-navigator-init", "extensionVersion": "1.0.8", "isRemote": false}, {"id": "@wsc-tee-decorate/shop-nav-h5~random2", "extensionName": "@wsc-tee-decorate/shop-nav-h5", "extensionVersion": "1.4.4", "stage": "pre", "isRemote": false}, {"id": "@wsc-tee-decorate/shop-nav-weapp~random2", "extensionName": "@wsc-tee-decorate/shop-nav-weapp", "extensionVersion": "1.4.4", "stage": "pre", "isRemote": false}, {"id": "@ext-tee-common/hummer~vFLoQpNY", "provide": {}, "consume": {"data": [{"name": "shopInfo", "origin": "APP", "originalName": "shop"}], "lambda": [{"name": "getUserInfo", "origin": "APP", "originalName": "getUserInfo"}]}, "extensionName": "@ext-tee-common/hummer", "extensionVersion": "1.0.1", "isRemote": false, "bindings": {"data.shopInfo": {"moduleId": "@wsc-tee-shop/shop-core~awkeKIEV", "name": "shop"}}}, {"id": "@ext-tee-wsc-decorate/theme-color~EKBphALO", "extensionName": "@ext-tee-wsc-decorate/theme-color", "extensionVersion": "1.4.4", "isRemote": false}, {"id": "@ext-tee-wsc-decorate/app-style~JkYyQAxD", "extensionName": "@ext-tee-wsc-decorate/app-style", "extensionVersion": "0.0.1", "isRemote": false}, {"id": "@ext-tee-wsc-decorate/elderly-oriented~E7586E9C", "extensionName": "@ext-tee-wsc-decorate/elderly-oriented", "extensionVersion": "1.0.0", "isRemote": false, "stage": "pre"}, {"id": "@wsc-tee-salesman/salesman-share-popup~feae1c02", "extensionName": "@wsc-tee-salesman/salesman-share-popup", "extensionVersion": "0.0.0", "isRemote": false, "bindings": {"widget.UserAuthorize": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Main"}]}}, {"id": "@wsc-tee-common/app-content-append~KLQlFFwg", "extensionName": "@wsc-tee-common/app-content-append", "extensionVersion": "0.0.0", "isRemote": false, "stage": "pre"}, {"id": "@wsc-tee-trade/trade-buy-id-card-popup~ZNFlmPZw", "extensionName": "@wsc-tee-trade/trade-buy-id-card-popup", "extensionVersion": "0.0.0", "isRemote": false}, {"id": "@ext-tee-user/fast-join-sdk~DDdJBmCG", "extensionName": "@ext-tee-user/fast-join-sdk", "extensionVersion": "0.0.0", "isRemote": false, "bindings": {"widget.FastJoin": [{"moduleId": "@ext-tee-user/fast-join-sdk~DDdJBmCG", "name": "Main"}], "widget.UserAuthorizePopup": [{"moduleId": "@passport-tee/user-authorize~FHzemhCn", "name": "Popup"}]}}, {"id": "@ext-tee-user/member-config~rWRLjWnK", "extensionName": "@ext-tee-user/member-config", "extensionVersion": "0.0.0", "isRemote": false}, {"id": "@wsc-tee-salesman/salesman-cloud-data~PSwHobiG", "extensionName": "@wsc-tee-salesman/salesman-cloud-data", "extensionVersion": "0.0.0", "isRemote": false}]}