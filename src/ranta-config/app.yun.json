{"rantaType": "app", "pageRoute": "pages/common/global-page/index", "nativePagePath": "packages/common/global-page/index", "grayRuleKey": "global-page", "cloudKey": "global-app", "extMapping": {"data": {}, "process": {}, "event": {}}, "components": {"user-auth": {"displayName": "用户授权组件", "description": "提供用户协议、手机号、头像昵称等授权功能的组件", "provider": "@passport-tee/user-authorize~FHzemhCn.CloudUserAuthorize", "props": {"authTypeList": {"type": "array", "description": "授权项列表"}, "isNeedUnionInfo": {"type": "boolean", "description": "是否需要获取用户全平台统一身份信息（仅 H5）。\n目前包含 unionId 信息。默认为 false，若设为 true，则可能会在必要的场景下唤起“昵称头像”授权弹窗来向用户申请获取相关信息。\n一般用于需要账号打通的场景，同一个开放平台账号主体下的不同公众号、小程序等的用户，授权后能识别为同一个用户，完成用户打通，提高用户识别率。\n启用此配置后会增加账号合并的概率，相关逻辑详见：https://doc.youzanyun.com/v2/doc/cloud/token/QKtUw6QMqiDwvFkM9Jbc6R96nZc"}}, "slots": {"default": {"displayName": "默认内容", "description": "默认内容。需要具有 UI，如文本、按钮或区块等，让授权组件整体可点击。样式可自定义。", "allowDelete": true, "allowMultiple": false}}}, "item-sku-popup": {"displayName": "商品规格弹层组件", "description": "提供规格选择、数量选择、加购和下单的组件", "provider": "@ext-tee-wsc-goods/biz-sku-manage.Main", "props": {"isShow": {"type": "boolean", "description": "是否显示"}, "alias": {"type": "string", "description": "商品别名"}, "actionBtns": {"type": "array", "description": "底部操作按钮"}, "limitActivityType": {"type": "array", "description": "指定营销活动类型(传all表示不参与所有活动)"}}, "slots": {"header": {"displayName": "头部", "description": "sku选择面板头部(包含图片、价格、库存)"}, "header-sku-price": {"displayName": "价格", "description": "价格"}}}, "id-card-popup": {"displayName": "身份证信息弹层组件", "description": "提供用户身份证信息填写功能，身份证信息包括：身份证姓名、身份证号码、身份证正反面照片等", "provider": "@wsc-tee-trade/trade-buy-id-card-popup~ZNFlmPZw.IdcardPopup", "props": {"isShow": {"type": "boolean", "description": "是否显示"}, "idCardInfo": {"type": "object", "description": "身份证信息"}, "idCardScene": {"type": "string", "description": "场景值"}, "isRequiredIdCardPhoto": {"type": "boolean", "description": "是否需要上传身份证照片"}, "isAllowEdit": {"type": "boolean", "description": "是否允许修改"}}}, "item-data": {"displayName": "商品数据组件", "description": "商品数据组件", "provider": "@ext-tee-wsc-goods/cloud-item-data.Main"}, "item-image": {"displayName": "商品头图组件", "description": "商品头图组件", "provider": "@ext-tee-wsc-goods/image-block.CloudItemImageFacade", "props": {"mainVideo": {"type": "object", "description": "商品视频"}, "images": {"type": "array", "description": "商品图片列表"}}}, "item-detail-content": {"displayName": "商品详情内容组件", "description": "商品详情内容组件", "provider": "@wsc-tee-decorate/showcase-container-h5.CloudGoodsDetailContent", "props": {"alias": {"type": "string", "description": "商品别名"}}}, "item-guarantee-detail-bar": {"displayName": "商品放心购组件", "description": "商品放心购组件", "provider": "@assets-tee-extensions/guarantee-detail-bar.CloudGuaranteeDetailBar"}, "fast-join-member-free-level": {"displayName": "会员快速入会组件", "description": "提供会员快速加入免费会员的组件", "provider": "@ext-tee-user/fast-join-sdk~DDdJBmCG.CloudFastJoin"}, "bottom-nav": {"displayName": "店铺底部导航组件", "description": "店铺底部导航组件", "provider": "@wsc-tee-decorate/shop-nav-weapp.BottomTabbar"}, "live-code-dialog": {"displayName": "活码展示弹窗组件", "description": "活码展示弹窗组件", "provider": "@ext-tee-user/live-code-add-fans.CloudLiveCodeDialog"}}, "slots": {"app-style": {"displayName": "应用级样式", "description": "应用级样式", "consumer": "@ext-tee-wsc-decorate/app-style~JkYyQAxD.Main", "allowDelete": false, "allowMultiple": true, "config": {"commonPriceFontWeight": {"type": "string", "description": "通用-金额字体字重"}, "commonFieldFontWeight": {"type": "string", "description": "通用-普通字体字重"}, "tradeAddressListPopupCardMarginLeft": {"type": "string", "description": "交易-地址弹窗-地址卡片左外边距"}, "tradeAddressListPopupCardMarginRight": {"type": "string", "description": "交易-地址弹窗-地址卡片右外边距"}, "tradeAddressListPopupCardMarginTop": {"type": "string", "description": "交易-地址弹窗-地址卡片上外边距"}, "tradeAddressListPopupCardMarginBottom": {"type": "string", "description": "交易-地址弹窗-地址卡片下外边距"}, "tradeAddressListPopupCardPaddingLeft": {"type": "string", "description": "交易-地址弹窗-地址卡片左内边距"}, "tradeAddressListPopupCardPaddingRight": {"type": "string", "description": "交易-地址弹窗-地址卡片右内边距"}, "tradeAddressListPopupCardPaddingTop": {"type": "string", "description": "交易-地址弹窗-地址卡片上内边距"}, "tradeAddressListPopupCardPaddingBottom": {"type": "string", "description": "交易-地址弹窗-地址卡片下内边距"}, "tradeSmGutter": {"type": "string", "description": "交易-亲密间距"}, "tradeMdGutter": {"type": "string", "description": "交易-并列间距"}, "goodsSkuNormalMargin": {"type": "string", "description": "商品-规格基本间距"}, "goodsSkuCardMargin": {"type": "string", "description": "商品-规格卡片间距"}, "goodsSkuRowItemSamePriceHeight": {"type": "string", "description": "商品-相同价格的规格卡片高度"}, "goodsSkuRowItemSamePricePaddingTop": {"type": "string", "description": "商品-相同价格的规格卡片上内边距"}, "goodsSkuRowItemSamePricePaddingBottom": {"type": "string", "description": "商品-相同价格的规格卡片下内边距"}, "goodsSkuRowItemSamePricePaddingLeft": {"type": "string", "description": "商品-相同价格的规格卡片左内边距"}, "goodsSkuRowItemSamePricePaddingRight": {"type": "string", "description": "商品-相同价格的规格卡片右内边距"}, "goodsSkuRowItemDiffPriceHeight": {"type": "string", "description": "商品-不同价格的规格卡片高度"}, "goodsSkuRowItemImgDiffPriceMinWidth": {"type": "string", "description": "商品-不同规格价格且有小图的规格宽度"}, "goodsSkuRowItemDiffPricePaddingTop": {"type": "string", "description": "商品-不同价格的规格卡片上内边距"}, "goodsSkuRowItemDiffPricePaddingBottom": {"type": "string", "description": "商品-相同价格的规格卡片下内边距"}, "goodsSkuRowItemDiffPricePaddingRight": {"type": "string", "description": "商品-相同价格的规格卡片右内边距"}, "goodsSkuRowItemDiffPricePaddingLeft": {"type": "string", "description": "商品-相同价格的规格卡片左内边距"}, "goodsSkuRowImgWidth": {"type": "string", "description": "商品-规格卡片中小图的图片宽度"}, "goodsSkuRowImgHeight": {"type": "string", "description": "商品-规格卡片中小图的图片高度"}, "goodsSkuCellPadding": {"type": "string", "description": "商品-规格单元格上下间距"}, "goodsSkuCardBackgroundColor": {"type": "string", "description": "商品-规格卡片、规格电子卡券填充色"}, "goodsSkuEcardPadding": {"type": "string", "description": "商品-规格电子卡券间距"}, "goodsSkuRowPaddingBottom": {"type": "string", "description": "商品-规格底部距离"}, "goodsStepperInputMargin": {"type": "string", "description": "商品-规格数量选择器外边距"}, "goodsStepperBorderWidth": {"type": "string", "description": "商品-规格数量选择器边框宽度"}, "goodsStepperBackgroundColor": {"type": "string", "description": "商品-规格数量选择器背景色"}, "goodsButtonMarginLeft": {"type": "string", "description": "商品-按钮间距（商品详情底部按钮、规格面板弹窗按钮）"}, "goodsBorderWidth": {"type": "string", "description": "商品-边框大小（商品详情底部按钮、规格面板弹窗按钮、规格卡片）"}, "goodsViceButtonBackgroundColor": {"type": "string", "description": "商品-副按钮填充色"}, "goodsUmpPriceTagPadding": {"type": "string", "description": "商品-营销价格标签左右间距"}, "umpCouponButtonHeight": {"type": "string", "description": "营销-优惠券按钮高度"}}}, "bottom-tabbar-bg": {"displayName": "底部导航背景", "description": "底部导航背景插槽", "consumer": "@wsc-tee-decorate/shop-nav-h5~random2.BottomTabbarH5Bg", "allowDelete": true}, "bottom-nav-bg": {"displayName": "店铺底部导航背景", "description": "店铺底部导航背景插槽", "consumer": "@wsc-tee-decorate/shop-nav-weapp~random2.BottomTabbarWeappBg", "allowDelete": true}, "user-auth-protocol-content": {"displayName": "用户协议授权弹窗定制内容", "description": "用户协议授权弹窗定制内容", "consumer": "@passport-tee/protocol~Duv5tmrK.CloudProtocolContent", "allowDelete": true, "allowMultiple": false}, "app-content-append": {"displayName": "应用级组件定制", "description": "应用级组件定制", "consumer": "@wsc-tee-common/app-content-append~KLQlFFwg.CloudAppContentAppend", "allowDelete": false, "allowMultiple": true}, "salesman-share-popup": {"displayName": "分销员分享弹窗", "description": "对分销员分享弹窗主体内容进行定制的组件", "consumer": "@wsc-tee-salesman/salesman-share-popup~feae1c02.CloudSalesmanSharePopup", "allowDelete": false, "allowMultiple": false}, "salesman-share-popup-header": {"displayName": "分销员分享弹窗头部", "description": "分销员分享弹窗组件中，对分销员分享弹窗头部内容进行定制的组件", "consumer": "@wsc-tee-salesman/salesman-share-popup~feae1c02.CloudSalesmanSharePopupHeader", "allowDelete": false, "allowMultiple": false}, "salesman-share-popup-tabs": {"displayName": "分销员分享弹窗tab", "description": "分销员分享弹窗组件中，对分销员分享弹窗tab进行定制的组件", "consumer": "@wsc-tee-salesman/salesman-share-popup~feae1c02.CloudSalesmanSharePopupTabs", "allowDelete": false, "allowMultiple": false}, "salesman-share-popup-custom-tab-content": {"displayName": "分销员分享弹窗自定义tab内容", "description": "分销员分享弹窗组件中，对分销员分享弹窗自定义tab内容进行定制的组件", "consumer": "@wsc-tee-salesman/salesman-share-popup~feae1c02.CloudSalesmanSharePopupCustomTabContent", "allowDelete": false, "allowMultiple": true, "props": {"tabType": {"type": "string", "description": "当前选中的tab的类型"}}}, "salesman-share-popup-footer": {"displayName": "分销员分享弹窗底部", "description": "分销员分享弹窗组件中，对分销员分享弹窗底部进行定制的组件", "consumer": "@wsc-tee-salesman/salesman-share-popup~feae1c02.CloudSalesmanSharePopupFooter", "allowDelete": false, "allowMultiple": false}}}