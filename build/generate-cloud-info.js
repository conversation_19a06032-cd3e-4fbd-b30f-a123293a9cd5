const { execa } = require('@kokojs/shared');

function getEnv() {
  const envStr = process.env.HARDWORKER_TASK_EXTRA_JSON || '{}';

  try {
    return JSON.parse(envStr);
  } catch (error) {}

  return {};
}

const env = getEnv();

async function startGenerateCloudInfo() {
  const { version } = env;

  console.log('开始生成有赞云配置和文档配置..... version: ' + version);

  if (!version) {
    console.log('未找到h5版本(commitId)，无法生成配置文件');
  }

  try {
    const result = await execa('koko', [
      'generate-cloud-info',
      `--version=${version}`,
      `--app=wsc-tee-h5`,
      `--platform=h5`,
    ]);
    console.log(result.stdout);
    console.log(result.stderr);
  } catch (error) {
    console.error(error);
  }
}

startGenerateCloudInfo();
