b-cov
t
!t/
*.seed
*.log
*.csv
*.dat
*.out
*.pid
*.gz
pids
logs
results
npm-debug.log
node_modules*
*.sqlite
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
analyze.json
ehthumbs.db
Thumbs.db
.idea/
wsc_dist*
dist*
ops-publish*
ranta-library.json
dist-showcase*
project.config.json
npm-debug.log.*
.cache-loader
package-lock.json
src/bower_components/
.editorconfig
.history/

.ranta
ranta
ranta-temp
src/packages/goods
src/pages-retail/*
src/packages/retail/*
!src/packages/retail/sales
!src/pages-retail/usercenter
!src/pages-retail/components
!src/pages-retail/util
!src/pages-retail/bear
!src/packages/retail/chat
!src/packages/retail/gps-location
!src/packages/retail/multi-shop
src/packages/trade/cart
tools/output/*.txt
tools/assets
ufile.json
app.ranta.js
.gitmodules
src/packages/trade/tee-demo
src/packages/tee-home
src/packages/trade/paid
src/packages/order/order-paid
src/packages/trade/trade
src/packages/transaction/order-paid
src/packages/transaction
src/packages/pay-result
src/packages/paid
src/packages/trade/order-detail
src/ext-tee-wsc-decorate
src/ext-tee-wsc-statcenter
src/ext-tee-assets
src/ext-tee-passport
src/ext-tee-shop
src/ext-tee-wsc-goods
src/ext-tee-wsc-ump
src/ext-tee-salesman
src/ext-tee-logger
src/ext-tee-wsc-im
src/ext-tee-wsc-trade
src/ext-tee-cps
src/ext-tee-retail-prepaid
src/ext-tee-guide
src/ext-tee-navigate
src/ext-tee-edu-goods
src/ext-tee-retail-groupbuy
src/ext-tee-retail-solitaire
src/ext-tee-wholesale
src/ext-tee-common
src/ext-tee-wsc-decorate-h5 

src/ranta-config/bizs/_*
src/ext-tee-user
src/ext-tee-wsc-decorate-h5
src/ext-tee-retail-shelf

ranta-base-library.json
__test__gateway
.jest
src/ext-tee-live
