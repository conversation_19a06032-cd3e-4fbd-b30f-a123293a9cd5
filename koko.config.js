const path = require('path');
const fs = require('fs');
const {
  isDev,
  isFastbuild,
  hash,
  isProd,
  getCacheDir,
} = require('@kokojs/shared');

const resolve = (src) => path.resolve(__dirname, src);

function getCacheVersion() {
  const packageJsonPath = path.resolve(__dirname, './package.json');
  const kokoConfigPath = path.resolve(__dirname, './koko.config.js');
  const jsonContent = fs.readFileSync(packageJsonPath, 'utf-8');
  const kokoConfigContent = fs.readFileSync(kokoConfigPath, 'utf-8');
  return hash(`${jsonContent}${kokoConfigContent}`);
}
function getPersistenceCacheDir(fastBuild) {
  const version = getCacheVersion();
  if (fastBuild) {
    return `/data/webpack-cache/wsc-h5-cache/${version}`;
  }
  return getCacheDir(`wsc-h5-cache/${version}`);
}

module.exports = {
  name: 'wsc-tee-h5',
  entry: [
    { path: 'src', files: ['app.js'] },
    { path: 'src', name: ['biz.config.js'] },
    {
      path: 'src',
      name: ['main.js'],
      exclude: /node_modules/,
      query: { tee: 'page' },
    },
  ],
  presets: ['ranta-tee-compiler'],
  presetOptions: {
    'ranta-tee-compiler': {
      useESBuildInDev: true,
    },
  },

  plugins: {
    'ranta-cloud': {},
    'jest-cloud': {},
    'ranta-tee': {
      multipleNpmVersions: ['@youzan/weapp-utils'],
    },
    babel: {
      checkType: false,
      compilePackages: [
        'query-string',
        'split-on-first',
        'strict-uri-encode',
        'hls.js',
      ],
    },
    hummer: {
      name: 'wsc-tee-h5',
      isMpApp: false,
    },
  },

  configureWebpack: {
    context: path.resolve(__dirname, 'src'),
    output: {
      // 原生中台化 dev 需要
      ...(isDev()
        ? {
            library: 'ranta_export_extension',
            chunkLoadingGlobal: 'webpackJsonpranta_export_extension',
          }
        : {}),
    },
    resolve: {
      alias: {
        '@youzan/vant-tee/dist': resolve(
          './node_modules/@youzan/vant-tee/dist'
        ),
        '@youzan/vant-tee': resolve('./node_modules/@youzan/vant-tee/dist'),
      },
    },
    cache: {
      type: 'filesystem',
      cacheDirectory: getPersistenceCacheDir(isFastbuild()),
    },
    snapshot: isProd() ? {} : { managedPaths: [] },
  },
};
